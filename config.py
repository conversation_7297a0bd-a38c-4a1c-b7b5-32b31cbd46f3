import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file (only in development)
# In production, environment variables are set by <PERSON><PERSON>
if not os.environ.get('RENDER'):
    load_dotenv()

# Helper function to determine environment
def is_production():
    """Check if the application is running in production mode"""
    return os.environ.get('FLASK_ENV') == 'production' or os.environ.get('RENDER') is not None

class Config:
    # Flask
    SECRET_KEY = os.environ.get('FLASK_SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'

    # Database
    DATABASE_PATH = 'tokens.db'

    # Database connection pool settings
    DB_POOL_SIZE = 5
    DB_MAX_OVERFLOW = 10
    DB_POOL_TIMEOUT = 30
    DB_POOL_RECYCLE = 1800  # 30 minutes

    # Google OAuth
    GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')
    GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET')
    GOOGLE_DISCOVERY_URL = 'https://accounts.google.com/.well-known/openid-configuration'
    GOOGLE_SCOPES = [
        'https://www.googleapis.com/auth/gmail.modify',
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/userinfo.email',
        'openid'
    ]

    # OpenRouter
    OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY')
    OPENROUTER_MODEL = 'meta-llama/llama-3.3-8b-instruct:free'

    # Email Agent
    DEFAULT_CHECK_INTERVAL = 10  # seconds
    MAX_CHECK_INTERVAL = 60  # seconds
    MAX_LOGS = 20  # Reduced from 50 to show fewer logs
    REPLY_PREFIX = 'Re: '

    # Log Settings
    IMPORTANT_LOG_TYPES = [
        "Agent started",
        "Agent stopped",
        "Found",
        "Processing:",
        "Reply sent",
        "Reply generated",
        "Error"
    ]

    # Rate Limiting
    RATELIMIT_DEFAULT = "300/minute"
    RATELIMIT_STORAGE_URL = "memory://"
    RATELIMIT_HEADERS_ENABLED = True

    # Session
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = 86400  # 24 hours

    # Security
    HTTPS_ENABLED = os.environ.get('HTTPS_ENABLED', 'True').lower() == 'true'

class ProductionConfig(Config):
    DEBUG = False
    TESTING = False

    # Production database settings
    DB_POOL_SIZE = 10
    DB_MAX_OVERFLOW = 20
    DB_POOL_RECYCLE = 1800  # 30 minutes

    # Rate limiting with Redis (if available)
    RATELIMIT_STORAGE_URL = os.environ.get('REDIS_URL', 'memory://')

    # Ensure HTTPS in production
    HTTPS_ENABLED = True
    SESSION_COOKIE_SECURE = True

    # Base URL for production
    BASE_URL = os.environ.get('BASE_URL', 'https://emailagentreplay.me')

class DevelopmentConfig(Config):
    DEBUG = True
    TESTING = False
    SESSION_COOKIE_SECURE = False  # Allow HTTP in development
    HTTPS_ENABLED = False

    # Smaller pool for development
    DB_POOL_SIZE = 3
    DB_MAX_OVERFLOW = 5

    # Base URL for development
    BASE_URL = os.environ.get('BASE_URL', 'http://localhost:5000')

class TestingConfig(Config):
    DEBUG = True
    TESTING = True
    SESSION_COOKIE_SECURE = False

    # Use in-memory SQLite for testing
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

# Set the active configuration
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# Determine environment
env = 'production' if is_production() else os.environ.get('FLASK_ENV', 'development')
active_config = config[env]()

# Print environment information (but not during testing)
if 'pytest' not in sys.modules:
    print(f"Running in {env.upper()} mode")
    print(f"Base URL: {active_config.BASE_URL}")
