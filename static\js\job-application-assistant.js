// Job Application Assistant - Frontend Logic
// Manages the multi-step wizard for job applications

class JobApplicationAssistant {
  constructor() {
    this.currentStep = 1;
    this.totalSteps = 7;
    this.formData = {};
    this.cvData = {};

    this.initializeEventListeners();
    this.updateProgressBar();
  }

  initializeEventListeners() {
    // CV File upload
    const cvFileInput = document.getElementById("cvFile");
    if (cvFileInput) {
      cvFileInput.addEventListener("change", this.handleCVUpload.bind(this));
    }

    // Navigation buttons
    for (let i = 1; i <= this.totalSteps; i++) {
      const nextBtn = document.getElementById(`jobAppNext${i}`);
      const backBtn = document.getElementById(`jobAppBack${i}`);

      if (nextBtn) {
        nextBtn.addEventListener("click", () => this.nextStep());
      }
      if (backBtn) {
        backBtn.addEventListener("click", () => this.prevStep());
      }
    }

    // Special buttons
    const summarizeBtn = document.getElementById("summarizeJobBtn");
    if (summarizeBtn) {
      summarizeBtn.addEventListener("click", this.summarizeJob.bind(this));
    }

    const regenerateBtn = document.getElementById("regenerateEmailBtn");
    if (regenerateBtn) {
      regenerateBtn.addEventListener("click", () => this.generateEmail(true));
    }

    const rephraseBtn = document.getElementById("rephraseEmailBtn");
    if (rephraseBtn) {
      rephraseBtn.addEventListener("click", () => this.rephraseEmail());
    }

    const restartBtn = document.getElementById("jobAppRestartBtn");
    if (restartBtn) {
      restartBtn.addEventListener("click", this.restart.bind(this));
    }

    // Form submission
    const form = document.getElementById("jobAppWizardForm");
    if (form) {
      form.addEventListener("submit", this.handleFormSubmit.bind(this));
    }

    // Real-time validation
    this.setupRealTimeValidation();
  }

  setupRealTimeValidation() {
    // Job description validation
    const jobDescInput = document.getElementById("jobDescription");
    if (jobDescInput) {
      jobDescInput.addEventListener("input", () => {
        const nextBtn = document.getElementById("jobAppNext3");
        if (nextBtn) {
          nextBtn.disabled = jobDescInput.value.trim().length < 10;
        }
      });
    }

    // HR email validation
    const hrEmailInput = document.getElementById("hrEmail");
    if (hrEmailInput) {
      hrEmailInput.addEventListener("input", () => {
        const nextBtn = document.getElementById("jobAppNext4");
        if (nextBtn) {
          const isValid = this.isValidEmail(hrEmailInput.value);
          nextBtn.disabled = !isValid;
          hrEmailInput.classList.toggle(
            "is-invalid",
            hrEmailInput.value && !isValid
          );
          hrEmailInput.classList.toggle("is-valid", isValid);
        }
      });
    }
  }

  async handleCVUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      this.showError("File size must be less than 5MB");
      event.target.value = "";
      return;
    }

    // Validate file type
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ];
    if (!allowedTypes.includes(file.type)) {
      this.showError("Please upload a PDF or DOCX file");
      event.target.value = "";
      return;
    }

    this.showParseStatus("Uploading and parsing CV...", "info");

    try {
      const formData = new FormData();
      formData.append("cv_file", file);

      const response = await fetch("/api/job-app/upload-cv", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        this.cvData = result.data;
        this.populateExtractedFields(result.data);
        this.showExtractedFields(true);
        this.showParseStatus("CV parsed successfully!", "success");
        document.getElementById("jobAppNext1").disabled = false;
      } else {
        throw new Error(result.error || "Failed to parse CV");
      }
    } catch (error) {
      console.error("CV upload error:", error);
      this.showParseStatus("Error parsing CV: " + error.message, "error");
      this.showExtractedFields(false);
      document.getElementById("jobAppNext1").disabled = true;
    }
  }
  populateExtractedFields(data) {
    // Fill in the extracted data with null checks
    const setFieldValue = (id, value) => {
      const element = document.getElementById(id);
      if (element) {
        element.value = value || "";
      } else {
        console.warn(`Element with ID '${id}' not found`);
      }
    };

    setFieldValue("cvFullName", data.full_name);
    setFieldValue("cvEmail", data.email);
    setFieldValue("cvPhone", data.phone);
    setFieldValue("cvLocation", data.location || data.address);
    setFieldValue("cvLinkedIn", data.linkedin_url);
    setFieldValue("cvEducation", data.education);
    setFieldValue("cvExperience", data.experience);
    setFieldValue("cvSummary", data.summary);

    // Handle skills - can be array or string
    let skills = data.skills || [];
    if (Array.isArray(skills)) {
      setFieldValue("cvSkills", skills.join(", "));
    } else {
      setFieldValue("cvSkills", skills);
    }
  }
  showExtractedFields(show) {
    const fieldsDiv = document.getElementById("extractedFields");
    if (fieldsDiv) {
      fieldsDiv.style.display = show ? "block" : "none";
    }
  }
  showParseStatus(message, type) {
    const statusDiv = document.getElementById("cvParseStatus");
    if (statusDiv) {
      statusDiv.textContent = message;
      statusDiv.className = `mb-3 text-${
        type === "error" ? "danger" : type === "success" ? "success" : "info"
      }`;
      statusDiv.style.display = "block";
    }
  }

  async summarizeJob() {
    const jobDesc = document.getElementById("jobDescription").value.trim();
    if (!jobDesc) {
      this.showError("Please enter a job description first");
      return;
    }

    const summarizeBtn = document.getElementById("summarizeJobBtn");
    const originalText = summarizeBtn.innerHTML;
    summarizeBtn.innerHTML =
      '<i class="bi bi-hourglass-split"></i> Summarizing...';
    summarizeBtn.disabled = true;

    try {
      const response = await fetch("/api/job-app/summarize-job", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ job_description: jobDesc }),
      });

      const result = await response.json();

      if (response.ok) {
        const summaryDiv = document.getElementById("jobSummary");
        summaryDiv.innerHTML = `<strong>Key Points:</strong><br>${result.summary}`;
        summaryDiv.classList.remove("d-none");
      } else {
        throw new Error(result.error || "Failed to summarize job");
      }
    } catch (error) {
      console.error("Job summarization error:", error);
      this.showError("Failed to summarize job: " + error.message);
    } finally {
      summarizeBtn.innerHTML = originalText;
      summarizeBtn.disabled = false;
    }
  }

  nextStep() {
    if (this.validateCurrentStep()) {
      this.collectStepData();

      if (this.currentStep === 5) {
        // Generate email when moving to step 6
        this.generateEmail();
      }

      this.currentStep++;
      this.showStep(this.currentStep);
      this.updateProgressBar();
    }
  }

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
      this.showStep(this.currentStep);
      this.updateProgressBar();
    }
  }
  validateCurrentStep() {
    switch (this.currentStep) {
      case 1:
        const extractedFields = document.getElementById("extractedFields");
        return (
          document.getElementById("cvFile").files.length > 0 &&
          extractedFields &&
          extractedFields.style.display !== "none"
        );
      case 3:
        return (
          document.getElementById("jobDescription").value.trim().length >= 10
        );
      case 4:
        return this.isValidEmail(document.getElementById("hrEmail").value);
      default:
        return true;
    }
  }

  collectStepData() {
    // Collect form data from current step
    const form = document.getElementById("jobAppWizardForm");
    const formData = new FormData(form);

    for (let [key, value] of formData.entries()) {
      this.formData[key] = value;
    } // Also collect edited CV data
    if (this.currentStep === 1) {
      const getFieldValue = (id) => {
        const element = document.getElementById(id);
        return element ? element.value : "";
      };

      this.formData.cv_data = {
        full_name: getFieldValue("cvFullName"),
        email: getFieldValue("cvEmail"),
        phone: getFieldValue("cvPhone"),
        location: getFieldValue("cvLocation"),
        linkedin_url: getFieldValue("cvLinkedIn"),
        education: getFieldValue("cvEducation"),
        experience: getFieldValue("cvExperience"),
        summary: getFieldValue("cvSummary"),
        skills: getFieldValue("cvSkills")
          .split(",")
          .map((s) => s.trim())
          .filter((s) => s),
      };

      const saveProfileCheckbox = document.getElementById(
        "saveProfileCheckbox"
      );
      this.formData.save_profile = saveProfileCheckbox
        ? saveProfileCheckbox.checked
        : false;
    }
  }

  async generateEmail(regenerate = false) {
    const statusDiv = document.getElementById("emailGenerationStatus");
    statusDiv.textContent = "Generating personalized email...";
    statusDiv.className = "mb-2 text-info";

    try {
      const payload = {
        cv_data: this.formData.cv_data,
        additional_info: this.formData.additional_info || "",
        job_description: this.formData.job_description,
        hr_name: this.formData.hr_name || "",
        hr_email: this.formData.hr_email,
        email_tone: this.formData.email_tone || "professional",
        email_language: this.formData.email_language || "en",
        email_type: this.formData.email_type || "application",
        regenerate: regenerate,
      };

      const response = await fetch("/api/job-app/generate-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (response.ok) {
        document.getElementById("emailSubject").value = result.subject;
        document.getElementById("emailBody").value = result.body;
        document.getElementById("emailSignature").value = result.signature;

        statusDiv.textContent =
          "Email generated successfully! Review and edit as needed.";
        statusDiv.className = "mb-2 text-success";
      } else {
        throw new Error(result.error || "Failed to generate email");
      }
    } catch (error) {
      console.error("Email generation error:", error);
      statusDiv.textContent = "Error generating email: " + error.message;
      statusDiv.className = "mb-2 text-danger";
    }
  }

  async rephraseEmail() {
    const currentBody = document.getElementById("emailBody").value;
    if (!currentBody.trim()) {
      this.showError("No email content to rephrase");
      return;
    }

    const rephraseBtn = document.getElementById("rephraseEmailBtn");
    const originalText = rephraseBtn.innerHTML;
    rephraseBtn.innerHTML =
      '<i class="bi bi-hourglass-split"></i> Rephrasing...';
    rephraseBtn.disabled = true;

    try {
      const response = await fetch("/api/job-app/rephrase-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email_body: currentBody,
          tone: this.formData.email_tone || "professional",
        }),
      });

      const result = await response.json();

      if (response.ok) {
        document.getElementById("emailBody").value = result.rephrased_body;
        this.showSuccess("Email rephrased successfully!");
      } else {
        throw new Error(result.error || "Failed to rephrase email");
      }
    } catch (error) {
      console.error("Email rephrase error:", error);
      this.showError("Failed to rephrase email: " + error.message);
    } finally {
      rephraseBtn.innerHTML = originalText;
      rephraseBtn.disabled = false;
    }
  }

  async handleFormSubmit(event) {
    event.preventDefault();

    const submitBtn = document.getElementById("sendJobAppBtn");
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Sending...';
    submitBtn.disabled = true;

    try {
      const emailData = {
        to_email: this.formData.hr_email,
        subject: document.getElementById("emailSubject").value,
        body: document.getElementById("emailBody").value,
        signature: document.getElementById("emailSignature").value,
        cv_data: this.formData.cv_data,
      };

      const response = await fetch("/api/job-app/send-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(emailData),
      });

      const result = await response.json();

      if (response.ok) {
        this.showResult(
          "success",
          "Email sent successfully!",
          `Your job application has been sent to ${this.formData.hr_email}`
        );
      } else {
        throw new Error(result.error || "Failed to send email");
      }
    } catch (error) {
      console.error("Email send error:", error);
      this.showResult("error", "Failed to send email", error.message);
    } finally {
      submitBtn.innerHTML = originalText;
      submitBtn.disabled = false;
      this.nextStep(); // Move to results step
    }
  }

  showResult(type, title, message) {
    const resultDiv = document.getElementById("jobAppResult");
    resultDiv.className = `alert alert-${
      type === "success" ? "success" : "danger"
    }`;
    resultDiv.innerHTML = `
            <h6><i class="bi bi-${
              type === "success" ? "check-circle" : "exclamation-circle"
            }"></i> ${title}</h6>
            <p class="mb-0">${message}</p>
        `;
    resultDiv.classList.remove("d-none");
  }

  showStep(stepNumber) {
    // Hide all steps
    for (let i = 1; i <= this.totalSteps; i++) {
      const step = document.getElementById(`jobAppStep${i}`);
      if (step) {
        step.classList.add("d-none");
      }
    }

    // Show current step
    const currentStepDiv = document.getElementById(`jobAppStep${stepNumber}`);
    if (currentStepDiv) {
      currentStepDiv.classList.remove("d-none");
    }
  }

  updateProgressBar() {
    const progress = (this.currentStep / this.totalSteps) * 100;
    const progressBar = document.getElementById("jobAppProgress");
    if (progressBar) {
      progressBar.style.width = `${progress}%`;
      progressBar.setAttribute("aria-valuenow", progress);
    }
  }
  restart() {
    this.currentStep = 1;
    this.formData = {};
    this.cvData = {};

    // Reset form
    document.getElementById("jobAppWizardForm").reset();

    // Hide extracted fields
    this.showExtractedFields(false);

    // Clear status messages
    const statusDiv = document.getElementById("cvParseStatus");
    if (statusDiv) {
      statusDiv.textContent = "";
      statusDiv.style.display = "none";
    }

    const summaryDiv = document.getElementById("jobSummary");
    if (summaryDiv) {
      summaryDiv.classList.add("d-none");
    }

    const resultDiv = document.getElementById("jobAppResult");
    if (resultDiv) {
      resultDiv.classList.add("d-none");
    }

    // Reset buttons
    document.getElementById("jobAppNext1").disabled = true;

    // Show first step
    this.showStep(1);
    this.updateProgressBar();
  }

  // Helper methods
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  showError(message) {
    // Use the existing toast system from dashboard
    if (typeof showToast === "function") {
      showToast("Error", message, "danger");
    } else {
      alert("Error: " + message);
    }
  }

  showSuccess(message) {
    // Use the existing toast system from dashboard
    if (typeof showToast === "function") {
      showToast("Success", message, "success");
    } else {
      alert("Success: " + message);
    }
  }
}

// Initialize the Job Application Assistant when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  if (
    document.querySelector(".job-application-assistant") ||
    document.getElementById("job-application-assistant")
  ) {
    window.jobAppAssistant = new JobApplicationAssistant();
  }
});
