// Job Assistant JavaScript

class JobAssistantWizard {
  constructor() {
    this.currentStep = 1;
    this.totalSteps = 6;
    this.cvData = null;
    this.applicationData = {};
    
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.updateStepDisplay();
  }

  setupEventListeners() {
    // Navigation buttons
    document.getElementById('nextBtn').addEventListener('click', () => this.nextStep());
    document.getElementById('prevBtn').addEventListener('click', () => this.prevStep());

    // CV Upload
    const cvUploadArea = document.getElementById('cvUploadArea');
    const cvFileInput = document.getElementById('cvFileInput');

    cvUploadArea.addEventListener('click', () => cvFileInput.click());
    cvUploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
    cvUploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
    cvUploadArea.addEventListener('drop', this.handleDrop.bind(this));
    
    cvFileInput.addEventListener('change', this.handleFileSelect.bind(this));

    // Change CV button
    document.getElementById('changeCvBtn').addEventListener('click', () => {
      this.resetCVUpload();
    });

    // View History button
    document.getElementById('viewHistoryBtn').addEventListener('click', () => {
      this.showApplicationHistory();
    });
  }

  handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('cvUploadArea').classList.add('dragover');
  }

  handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('cvUploadArea').classList.remove('dragover');
  }

  handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('cvUploadArea').classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      this.processFile(files[0]);
    }
  }

  handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
      this.processFile(file);
    }
  }

  processFile(file) {
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      this.showToast('Error', 'Please upload a PDF or DOCX file.', 'danger');
      return;
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      this.showToast('Error', 'File size must be less than 10MB.', 'danger');
      return;
    }

    this.uploadCV(file);
  }

  async uploadCV(file) {
    this.showProcessing('Uploading and parsing your CV...');

    const formData = new FormData();
    formData.append('cv_file', file);

    try {
      const response = await fetch('/api/job_assistant/upload_cv', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      console.log('Raw API Response:', result);

      if (result.success) {
        console.log('CV Data received from API:', result.data);
        this.cvData = result.data;
        this.showCVPreview(file, result.data);
        this.enableNextButton();
        this.showToast('Success', 'CV uploaded and parsed successfully!', 'success');
      } else if (result.partial) {
        this.showPartialExtractionAlert(result.message);
      } else {
        this.showToast('Error', result.message || 'Failed to process CV.', 'danger');
      }
    } catch (error) {
      console.error('Error uploading CV:', error);
      this.showToast('Error', 'Failed to upload CV. Please try again.', 'danger');
    } finally {
      this.hideProcessing();
    }
  }

  showPartialExtractionAlert(message) {
    // Hide preview and info
    document.getElementById('cvPreview').style.display = 'none';
    document.getElementById('extractedInfo').style.display = 'none';
    // Show alert and re-upload button
    const infoGrid = document.getElementById('infoGrid');
    infoGrid.innerHTML = `
      <div class="alert alert-warning" role="alert">
        <strong>Extraction Issue:</strong> ${message}
      </div>
      <button class="btn btn-primary mt-2" id="reuploadCvBtn">Re-upload CV</button>
    `;
    document.getElementById('cvUploadArea').style.display = 'block';
    // Add event listener for re-upload
    setTimeout(() => {
      const btn = document.getElementById('reuploadCvBtn');
      if (btn) btn.onclick = () => this.resetCVUpload();
    }, 0);
  }

  showCVPreview(file, data) {
    console.log('Data received in showCVPreview:', data);
    const cvPreview = document.getElementById('cvPreview');
    const extractedInfo = document.getElementById('extractedInfo');
    const cvInfo = document.getElementById('cvInfo');
    const infoGrid = document.getElementById('infoGrid');

    // Show CV file info
    cvInfo.innerHTML = `
      <div class="cv-info">
        <div class="cv-info-item">
          <strong>Filename:</strong> ${file.name}
        </div>
        <div class="cv-info-item">
          <strong>Size:</strong> ${this.formatFileSize(file.size)}
        </div>
        <div class="cv-info-item">
          <strong>Type:</strong> ${file.type.includes('pdf') ? 'PDF' : 'Word Document'}
        </div>
      </div>
    `;

    // Show extracted information
    console.log('Education data before formatting:', data.education);
    console.log('Work experience data before formatting:', data.work_experience);
    infoGrid.innerHTML = this.generateInfoGrid(data);

    cvPreview.style.display = 'block';
    extractedInfo.style.display = 'block';
    document.getElementById('cvUploadArea').style.display = 'none';
  }

  generateInfoGrid(data) {
    console.log('Data in generateInfoGrid:', data);
    return `
      <div class="info-section">
        <h6><i class="bi bi-person"></i> Personal Information</h6>
        <div class="mb-3">
          <label class="form-label">Full Name</label>
          <input type="text" class="editable-field" id="fullName" value="${data.full_name || ''}" placeholder="Enter your full name">
        </div>
        <div class="mb-3">
          <label class="form-label">Email</label>
          <input type="email" class="editable-field" id="email" value="${data.email || ''}" placeholder="Enter your email">
        </div>
        <div class="mb-3">
          <label class="form-label">Phone</label>
          <input type="text" class="editable-field" id="phone" value="${data.phone || ''}" placeholder="Enter your phone number">
        </div>
      </div>
      
      <div class="info-section">
        <h6><i class="bi bi-geo-alt"></i> Address</h6>
        <textarea class="editable-field" id="address" rows="3" placeholder="Enter your address">${data.address || ''}</textarea>
      </div>
      
      <div class="info-section">
        <h6><i class="bi bi-mortarboard"></i> Education</h6>
        <textarea class="editable-field" id="education" rows="4" placeholder="Enter your education details">${this.formatEducation(data.education)}</textarea>
      </div>
      
      <div class="info-section">
        <h6><i class="bi bi-briefcase"></i> Work Experience</h6>
        <textarea class="editable-field" id="workExperience" rows="5" placeholder="Enter your work experience">${this.formatWorkExperience(data.work_experience)}</textarea>
      </div>
      
      <div class="info-section">
        <h6><i class="bi bi-tools"></i> Skills</h6>
        <textarea class="editable-field" id="skills" rows="3" placeholder="Enter your skills">${this.formatArrayData(data.skills)}</textarea>
      </div>
      
      <div class="info-section">
        <h6><i class="bi bi-translate"></i> Languages</h6>
        <textarea class="editable-field" id="languages" rows="2" placeholder="Enter languages you speak">${this.formatArrayData(data.languages)}</textarea>
      </div>
    `;
  }

  formatArrayData(data) {
    if (Array.isArray(data)) {
      // If array of objects, fallback to JSON string
      if (data.length > 0 && typeof data[0] === 'object') {
        return data.map(obj => JSON.stringify(obj)).join('\n');
      }
      return data.join('\n');
    }
    return data || '';
  }

  formatEducation(education) {
    console.log('formatEducation input:', education);
    console.log('formatEducation input type:', typeof education);
    
    if (!education) {
      console.log('No education data provided');
      return '';
    }
    
    // If it's a string, try to parse it as JSON
    if (typeof education === 'string') {
      console.log('Education is a string, attempting to parse JSON');
      try {
        education = JSON.parse(education);
        console.log('Successfully parsed education JSON:', education);
      } catch (e) {
        console.log('Failed to parse education JSON, returning as string');
        return education;
      }
    }
    
    // If it's not an array, make it an array
    if (!Array.isArray(education)) {
      console.log('Converting single education item to array');
      education = [education];
    }
    
    const formatted = education.map(e => {
      console.log('Processing education item:', e);
      if (typeof e === 'string') return e;
      // Format object nicely
      let str = '';
      if (e.degree) str += e.degree;
      if (e.institution) str += (str ? ', ' : '') + e.institution;
      if (e.year) str += (str ? ' (' : '(') + e.year + ')';
      if (e.specialization) str += ' - ' + e.specialization;
      if (e.thesis) str += ' | Thesis: ' + e.thesis;
      if (e.relevant_coursework) str += ' | Coursework: ' + e.relevant_coursework;
      console.log('Formatted education item:', str);
      return str;
    }).join('\n');
    
    console.log('Final formatted education:', formatted);
    return formatted;
  }

  formatWorkExperience(workExperience) {
    console.log('formatWorkExperience input:', workExperience);
    console.log('formatWorkExperience input type:', typeof workExperience);
    
    if (!workExperience) {
      console.log('No work experience data provided');
      return '';
    }
    
    // If it's a string, try to parse it as JSON
    if (typeof workExperience === 'string') {
      console.log('Work experience is a string, attempting to parse JSON');
      try {
        workExperience = JSON.parse(workExperience);
        console.log('Successfully parsed work experience JSON:', workExperience);
      } catch (e) {
        console.log('Failed to parse work experience JSON, returning as string');
        return workExperience;
      }
    }
    
    // If it's not an array, make it an array
    if (!Array.isArray(workExperience)) {
      console.log('Converting single work experience item to array');
      workExperience = [workExperience];
    }
      const formatted = workExperience.map(exp => {
      console.log('Processing work experience item:', exp);
      if (typeof exp === 'string') return exp;
      // Format object nicely
      let str = '';
      if (exp.position || exp.title) str += (exp.position || exp.title);
      if (exp.company) str += (str ? ' at ' : '') + exp.company;
      if (exp.duration) str += (str ? ' (' : '(') + exp.duration + ')';
      if (exp.description) str += '\n' + exp.description;
      console.log('Formatted work experience item:', str);
      return str;
    }).join('\n\n');
    
    console.log('Final formatted work experience:', formatted);
    return formatted;
  }

  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  resetCVUpload() {
    document.getElementById('cvUploadArea').style.display = 'block';
    document.getElementById('cvPreview').style.display = 'none';
    document.getElementById('extractedInfo').style.display = 'none';
    document.getElementById('cvFileInput').value = '';
    this.cvData = null;
    this.disableNextButton();
  }

  nextStep() {
    if (this.currentStep < this.totalSteps) {
      // Validate current step before proceeding
      if (this.validateCurrentStep()) {
        this.currentStep++;
        this.updateStepDisplay();
        this.loadStepContent();
      }
    }
  }

  prevStep() {
    if (this.currentStep > 1) {
      this.currentStep--;
      this.updateStepDisplay();
      this.loadStepContent();
    }
  }

  validateCurrentStep() {
    switch (this.currentStep) {
      case 1:
        return this.cvData !== null;
      case 2:
        // Additional info is optional
        return true;
      case 3:
        // Validate job details
        return this.validateJobDetails();
      case 4:
        // Validate HR contact
        return this.validateHRContact();
      case 5:
        // Validate preferences
        return this.validatePreferences();
      default:
        return true;
    }
  }

  validateJobDetails() {
    const companyName = document.getElementById('companyName')?.value.trim();
    const positionTitle = document.getElementById('positionTitle')?.value.trim();

    const isValid = companyName && positionTitle;

    if (isValid) {
      this.enableNextButton();
    } else {
      this.disableNextButton();
    }

    return isValid;
  }

  validateHRContact() {
    const hrEmail = document.getElementById('hrEmail')?.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    const isValid = hrEmail && emailRegex.test(hrEmail);

    if (isValid) {
      this.enableNextButton();
      this.showContactVerification();
    } else {
      this.disableNextButton();
      this.hideContactVerification();
    }

    return isValid;
  }

  validatePreferences() {
    // Preferences are always valid as they have default values
    return true;
  }

  async analyzeJobDescription() {
    const jobDescription = document.getElementById('jobDescription')?.value.trim();

    if (jobDescription && jobDescription.length > 100) {
      try {
        this.showProcessing('Analyzing job description...');

        const response = await fetch('/api/job_assistant/analyze_job', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            job_description: jobDescription
          })
        });

        const result = await response.json();

        if (result.success) {
          this.showJobSummary(result.data);
        }
      } catch (error) {
        console.error('Error analyzing job description:', error);
      } finally {
        this.hideProcessing();
      }
    }
  }

  showJobSummary(data) {
    const jobSummary = document.getElementById('jobSummary');
    const jobSummaryContent = document.getElementById('jobSummaryContent');

    if (jobSummary && jobSummaryContent) {
      jobSummaryContent.innerHTML = `
        <div class="summary-grid">
          <div class="summary-item">
            <strong>Key Requirements:</strong>
            <ul>
              ${data.requirements.map(req => `<li>${req}</li>`).join('')}
            </ul>
          </div>
          <div class="summary-item">
            <strong>Recommended Skills to Highlight:</strong>
            <ul>
              ${data.skills_to_highlight.map(skill => `<li>${skill}</li>`).join('')}
            </ul>
          </div>
        </div>
      `;
      jobSummary.style.display = 'block';
    }
  }

  showContactVerification() {
    const verification = document.getElementById('contactVerification');
    if (verification) {
      verification.style.display = 'block';
    }
  }

  hideContactVerification() {
    const verification = document.getElementById('contactVerification');
    if (verification) {
      verification.style.display = 'none';
    }
  }

  updateTonePreview() {
    const tone = document.getElementById('emailTone')?.value || 'professional';
    const previewCard = document.getElementById('previewCard');

    const toneExamples = {
      professional: "I am writing to express my interest in the [Position] role at [Company]. With my background in [Field], I believe I would be a valuable addition to your team.",
      friendly: "Hi there! I'm excited to apply for the [Position] position at [Company]. I think my experience in [Field] would be a great fit for what you're looking for.",
      enthusiastic: "I'm thrilled to apply for the [Position] role at [Company]! My passion for [Field] and proven track record make me an ideal candidate for this exciting opportunity.",
      confident: "I am the ideal candidate for the [Position] role at [Company]. My extensive experience in [Field] and proven results demonstrate my ability to excel in this position."
    };

    if (previewCard) {
      previewCard.innerHTML = `
        <div class="preview-content">
          <strong>${tone.charAt(0).toUpperCase() + tone.slice(1)}:</strong> "${toneExamples[tone]}"
        </div>
      `;
    }
  }

  showApplicationSummary() {
    const summaryContainer = document.getElementById('applicationSummary');

    if (summaryContainer) {
      summaryContainer.innerHTML = `
        <div class="summary-section">
          <h6><i class="bi bi-person"></i> Personal Information</h6>
          <div class="summary-item">
            <span class="summary-label">Name:</span>
            <span class="summary-value">${this.cvData?.full_name || 'Not provided'}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Email:</span>
            <span class="summary-value">${this.cvData?.email || 'Not provided'}</span>
          </div>
        </div>

        <div class="summary-section">
          <h6><i class="bi bi-briefcase"></i> Job Details</h6>
          <div class="summary-item">
            <span class="summary-label">Company:</span>
            <span class="summary-value">${this.applicationData.company_name || 'Not provided'}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Position:</span>
            <span class="summary-value">${this.applicationData.position_title || 'Not provided'}</span>
          </div>
        </div>

        <div class="summary-section">
          <h6><i class="bi bi-envelope"></i> Contact & Preferences</h6>
          <div class="summary-item">
            <span class="summary-label">HR Email:</span>
            <span class="summary-value">${this.applicationData.hr_email || 'Not provided'}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Tone:</span>
            <span class="summary-value">${this.applicationData.email_tone || 'Professional'}</span>
          </div>
          <div class="summary-item">
            <span class="summary-label">Language:</span>
            <span class="summary-value">${this.applicationData.email_language || 'English'}</span>
          </div>
        </div>
      `;
    }
  }

  async generateEmail() {
    try {
      this.showProcessing('Generating your personalized email...');

      // Collect updated CV data from form fields
      const updatedCvData = this.collectUpdatedCvData();

      const requestData = {
        cv_data: updatedCvData,
        application_data: this.applicationData
      };

      const response = await fetch('/api/job_assistant/generate_email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      const result = await response.json();

      if (result.success) {
        this.showGeneratedEmail(result.data);
        this.setupEmailActions(result.data.id);
      } else {
        this.showToast('Error', result.message || 'Failed to generate email', 'danger');
      }
    } catch (error) {
      console.error('Error generating email:', error);
      this.showToast('Error', 'Failed to generate email. Please try again.', 'danger');
    } finally {
      this.hideProcessing();
    }
  }

  collectUpdatedCvData() {
    return {
      full_name: document.getElementById('fullName')?.value || '',
      email: document.getElementById('email')?.value || '',
      phone: document.getElementById('phone')?.value || '',
      address: document.getElementById('address')?.value || '',
      education: document.getElementById('education')?.value || '',
      work_experience: document.getElementById('workExperience')?.value || '',
      skills: document.getElementById('skills')?.value || '',
      languages: document.getElementById('languages')?.value || '',
      additional_info: document.getElementById('additionalInfo')?.value || ''
    };
  }

  updateStepDisplay() {
    // Update progress steps
    document.querySelectorAll('.step').forEach((step, index) => {
      const stepNumber = index + 1;
      step.classList.remove('active', 'completed');
      
      if (stepNumber < this.currentStep) {
        step.classList.add('completed');
      } else if (stepNumber === this.currentStep) {
        step.classList.add('active');
      }
    });

    // Update wizard steps
    document.querySelectorAll('.wizard-step').forEach((step, index) => {
      step.classList.remove('active');
      if (index + 1 === this.currentStep) {
        step.classList.add('active');
      }
    });

    // Update navigation buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    prevBtn.style.display = this.currentStep > 1 ? 'block' : 'none';
    
    if (this.currentStep === this.totalSteps) {
      nextBtn.innerHTML = '<i class="bi bi-send"></i> Generate & Send Email';
      nextBtn.classList.remove('btn-primary');
      nextBtn.classList.add('btn-success');
    } else {
      nextBtn.innerHTML = 'Next <i class="bi bi-arrow-right"></i>';
      nextBtn.classList.remove('btn-success');
      nextBtn.classList.add('btn-primary');
    }
  }

  loadStepContent() {
    // This will be expanded to load content for each step
    switch (this.currentStep) {
      case 2:
        this.loadAdditionalInfoStep();
        break;
      case 3:
        this.loadJobDetailsStep();
        break;
      case 4:
        this.loadHRContactStep();
        break;
      case 5:
        this.loadPreferencesStep();
        break;
      case 6:
        this.loadReviewStep();
        break;
    }
  }

  loadAdditionalInfoStep() {
    // Enable next button for optional step
    this.enableNextButton();

    // Add event listener for additional info textarea
    const additionalInfo = document.getElementById('additionalInfo');
    if (additionalInfo) {
      additionalInfo.addEventListener('input', () => {
        this.applicationData.additional_info = additionalInfo.value;
      });
    }
  }

  loadJobDetailsStep() {
    this.disableNextButton();

    // Add event listeners for job details
    const companyName = document.getElementById('companyName');
    const positionTitle = document.getElementById('positionTitle');
    const jobDescription = document.getElementById('jobDescription');

    if (companyName) {
      companyName.addEventListener('input', () => {
        this.applicationData.company_name = companyName.value;
        this.validateJobDetails();
      });
    }

    if (positionTitle) {
      positionTitle.addEventListener('input', () => {
        this.applicationData.position_title = positionTitle.value;
        this.validateJobDetails();
      });
    }

    if (jobDescription) {
      jobDescription.addEventListener('input', () => {
        this.applicationData.job_description = jobDescription.value;
        this.analyzeJobDescription();
      });
    }
  }

  loadHRContactStep() {
    this.disableNextButton();

    // Add event listeners for HR contact
    const hrEmail = document.getElementById('hrEmail');
    const hrName = document.getElementById('hrName');

    if (hrEmail) {
      hrEmail.addEventListener('input', () => {
        this.applicationData.hr_email = hrEmail.value;
        this.validateHRContact();
      });
    }

    if (hrName) {
      hrName.addEventListener('input', () => {
        this.applicationData.hr_name = hrName.value;
      });
    }
  }

  loadPreferencesStep() {
    this.enableNextButton();

    // Add event listeners for preferences
    const emailTone = document.getElementById('emailTone');
    const emailLanguage = document.getElementById('emailLanguage');
    const emailType = document.getElementById('emailType');

    if (emailTone) {
      emailTone.addEventListener('change', () => {
        this.applicationData.email_tone = emailTone.value;
        this.updateTonePreview();
      });
    }

    if (emailLanguage) {
      emailLanguage.addEventListener('change', () => {
        this.applicationData.email_language = emailLanguage.value;
      });
    }

    if (emailType) {
      emailType.addEventListener('change', () => {
        this.applicationData.email_type = emailType.value;
      });
    }

    // Initialize tone preview
    this.updateTonePreview();
  }

  loadReviewStep() {
    this.showApplicationSummary();
    this.generateEmail();
  }

  enableNextButton() {
    document.getElementById('nextBtn').disabled = false;
  }

  disableNextButton() {
    document.getElementById('nextBtn').disabled = true;
  }

  showProcessing(message = 'Processing...') {
    const overlay = document.getElementById('processingOverlay');
    const text = overlay.querySelector('.processing-text');
    text.textContent = message;
    overlay.classList.add('show');
  }

  hideProcessing() {
    document.getElementById('processingOverlay').classList.remove('show');
  }

  showToast(title, message, type = 'info') {
    // Use the existing toast function from unified-dashboard.js if available
    if (typeof showToast === 'function') {
      showToast(title, message, type);
    } else {
      // Fallback alert
      alert(`${title}: ${message}`);
    }
  }

  showGeneratedEmail(emailData) {
    const generatedEmail = document.getElementById('generatedEmail');
    const emailTo = document.getElementById('emailTo');
    const emailSubject = document.getElementById('emailSubject');
    const emailBody = document.getElementById('emailBody');
    const emailSignature = document.getElementById('emailSignature');

    if (generatedEmail && emailTo && emailSubject && emailBody && emailSignature) {
      emailTo.textContent = this.applicationData.hr_email;
      emailSubject.textContent = emailData.subject;
      emailBody.innerHTML = emailData.body.replace(/\n/g, '<br>');
      emailSignature.innerHTML = emailData.signature.replace(/\n/g, '<br>');

      generatedEmail.style.display = 'block';

      // Store email ID for later use
      this.currentEmailId = emailData.id;
    }
  }

  setupEmailActions(emailId) {
    const regenerateBtn = document.getElementById('regenerateBtn');
    const sendEmailBtn = document.getElementById('sendEmailBtn');

    if (regenerateBtn) {
      regenerateBtn.addEventListener('click', () => this.regenerateEmail(emailId));
    }

    if (sendEmailBtn) {
      sendEmailBtn.addEventListener('click', () => this.sendEmail(emailId));
    }
  }

  async regenerateEmail(emailId) {
    try {
      this.showProcessing('Regenerating email with different approach...');

      const response = await fetch(`/api/job_assistant/regenerate_email/${emailId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          modified_body: document.getElementById('emailBody')?.innerHTML || ''
        })
      });

      const result = await response.json();

      if (result.success) {
        this.showGeneratedEmail(result.data);
        this.showToast('Success', 'Email regenerated successfully!', 'success');
      } else {
        this.showToast('Error', result.message || 'Failed to regenerate email', 'danger');
      }
    } catch (error) {
      console.error('Error regenerating email:', error);
      this.showToast('Error', 'Failed to regenerate email. Please try again.', 'danger');
    } finally {
      this.hideProcessing();
    }
  }

  async sendEmail(emailId) {
    if (!confirm('Are you sure you want to send this application email?')) {
      return;
    }

    try {
      const sendBtn = document.getElementById('sendEmailBtn');
      if (sendBtn) {
        sendBtn.classList.add('loading');
        sendBtn.disabled = true;
      }

      // Get the current email body (in case user edited it)
      const modifiedBody = document.getElementById('emailBody')?.innerHTML || '';

      const response = await fetch(`/api/job_assistant/send_email/${emailId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          modified_body: modifiedBody
        })
      });

      const result = await response.json();

      if (result.success) {
        this.showToast('Success', 'Application email sent successfully!', 'success');

        // Update button to show success
        if (sendBtn) {
          sendBtn.innerHTML = '<i class="bi bi-check-circle"></i> Email Sent!';
          sendBtn.classList.remove('btn-success');
          sendBtn.classList.add('btn-outline-success');
        }

        // Optionally redirect to dashboard or show next steps
        setTimeout(() => {
          if (confirm('Email sent successfully! Would you like to create another application?')) {
            window.location.reload();
          }
        }, 2000);

      } else {
        this.showToast('Error', result.message || 'Failed to send email', 'danger');

        if (sendBtn) {
          sendBtn.classList.remove('loading');
          sendBtn.disabled = false;
        }
      }
    } catch (error) {
      console.error('Error sending email:', error);
      this.showToast('Error', 'Failed to send email. Please try again.', 'danger');

      const sendBtn = document.getElementById('sendEmailBtn');
      if (sendBtn) {
        sendBtn.classList.remove('loading');
        sendBtn.disabled = false;
      }
    }
  }

  showApplicationHistory() {
    // This will be implemented to show application history
    this.showToast('Info', 'Application history feature coming soon!', 'info');
  }
}

// Initialize the wizard when the page loads
document.addEventListener('DOMContentLoaded', function() {
  new JobAssistantWizard();
});
