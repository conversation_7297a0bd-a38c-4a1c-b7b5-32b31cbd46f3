from flask import Flask, render_template, redirect, url_for, session, jsonify, request, flash, make_response
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_migrate import Migrate
from google_auth_oauthlib.flow import Flow
from google.oauth2.credentials import Credentials  # Used in oauth2callback
from google.auth.transport.requests import Request  # Used in oauth2callback
from googleapiclient.discovery import build
from models import db, User, OAuthToken, UserSettings, UserLog, CVProfile, JobApplication, GeneratedJobEmail
import os
import json  # Used in oauth2callback
import threading
import time
import re
import psycopg2
import psycopg2.extras
from datetime import datetime, timezone
from openai import OpenAI
from admin_utils import admin_required
from werkzeug.utils import secure_filename
import PyPDF2
import docx
import tempfile
# Config handles loading dotenv
from agent import EmailAgent, agents

# Import configuration first to set up environment
from config import active_config as config, is_production

# Import Flask-Compress for response compression
from flask_compress import Compress

# SEO Helper Functions
def get_base_url():
    """Get the base URL for the application"""
    return config.BASE_URL

def get_canonical_url(endpoint, **values):
    """Generate canonical URL for a given endpoint"""
    base_url = get_base_url()
    if endpoint == 'index':
        return base_url
    return f"{base_url}{url_for(endpoint, **values)}"

def get_structured_data(page_type, **kwargs):
    """Generate structured data for better search engine understanding"""
    base_url = get_base_url()
    
    # Organization data
    organization_data = {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Email Reply Agent",
        "url": base_url,
        "logo": f"{base_url}/static/img/og-image.jpg",
        "description": "AI-powered email automation for Gmail users",
        "sameAs": [
            # Add your social media URLs here when available
        ]
    }
    
    # Website data
    website_data = {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Email Reply Agent",
        "url": base_url,
        "potentialAction": {
            "@type": "SearchAction",
            "target": f"{base_url}/search?q={{search_term_string}}",
            "query-input": "required name=search_term_string"
        }
    }
    
    # Software Application data
    software_data = {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": "Email Reply Agent",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "description": "Free trial available"
        },
        "creator": organization_data,
        "description": "Automate your Gmail replies with AI-powered intelligent responses"
    }
    
    # Page-specific structured data
    if page_type == 'landing':
        software_data.update({
            "description": "AI-powered email automation tool for Gmail users that generates intelligent responses and saves time",
            "browserRequirements": "Requires JavaScript. Compatible with Chrome, Firefox, Safari, Edge",
            "softwareVersion": "1.0",
            "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "USD",
                "priceValidUntil": "2025-12-31"
            },
            "provider": organization_data,
            "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "ratingCount": "127",
                "bestRating": "5"
            },
            "featureList": [
                "AI-powered email responses",
                "Gmail integration",
                "Preview and edit replies",
                "Automated email management",
                "Real-time notifications"
            ]
        })
    
    # Breadcrumb data
    breadcrumbs = kwargs.get('breadcrumbs', [])
    breadcrumb_data = None
    if breadcrumbs:
        breadcrumb_data = {
            "@context": "https://schema.org",
            "@type": "BreadcrumbList",
            "itemListElement": []
        }
        
        for i, breadcrumb in enumerate(breadcrumbs):
            item = {
                "@type": "ListItem",
                "position": i + 1,
                "name": breadcrumb.get("name", ""),
            }
            if breadcrumb.get("url"):
                item["item"] = breadcrumb["url"]
            breadcrumb_data["itemListElement"].append(item)
    
    # Combine all structured data
    structured_data = [organization_data, website_data, software_data]
    if breadcrumb_data:
        structured_data.append(breadcrumb_data)
    
    return structured_data

def get_breadcrumbs(page_name, custom_breadcrumbs=None):
    """Generate breadcrumb navigation"""
    base_url = get_base_url()
    
    if custom_breadcrumbs:
        return custom_breadcrumbs
    
    breadcrumbs = [
        {"name": "Home", "url": base_url}
    ]
    
    if page_name and page_name != "Home":
        breadcrumbs.append({"name": page_name, "url": None})
    
    return breadcrumbs

def get_seo_data(page_type, **kwargs):
    """Generate SEO data for different page types"""
    base_url = get_base_url()

    seo_data = {
        'site_name': 'Email Reply Agent',
        'base_url': base_url,
        'og_image': f"{base_url}/static/img/og-image.jpg",
        'twitter_image': f"{base_url}/static/img/twitter-card.jpg",
        'twitter_site': '@emailreplyagent',  # Update with actual Twitter handle
        'structured_data': get_structured_data(page_type, **kwargs),
        'breadcrumbs': get_breadcrumbs(kwargs.get('page_name'))
    }

    if page_type == 'landing':
        seo_data.update({
            'title': 'Email Reply Agent - Automate Your Gmail Replies with AI',
            'description': 'Transform your email workflow with AI-powered automation. Our intelligent email assistant analyzes incoming messages and generates contextually appropriate responses, saving you hours every week.',
            'keywords': 'email automation, AI email replies, Gmail automation, email productivity, intelligent email assistant, automated responses, email management, AI assistant',
            'og_title': 'Automate Your Email Replies with AI - Email Reply Agent',
            'og_description': 'Save hours every week with our intelligent email automation. Preview, edit, and approve AI-generated replies with a single click.',
            'twitter_title': 'Email Reply Agent - AI-Powered Email Automation',
            'twitter_description': 'Transform your Gmail workflow with intelligent AI responses. Save time and boost productivity.',
            'breadcrumbs': get_breadcrumbs('Home')
        })
    elif page_type == 'login':
        seo_data.update({
            'title': 'Login - Email Reply Agent',
            'description': 'Sign in to your Email Reply Agent account to access AI-powered email automation and manage your Gmail replies efficiently.',
            'keywords': 'login, sign in, email agent, Gmail automation, email management',
            'og_title': 'Login to Email Reply Agent',
            'og_description': 'Access your AI-powered email automation dashboard.',
            'twitter_title': 'Login - Email Reply Agent',
            'twitter_description': 'Sign in to manage your automated email replies.',
            'breadcrumbs': get_breadcrumbs('Login')
        })
    elif page_type == 'signup':
        seo_data.update({
            'title': 'Sign Up - Email Reply Agent',
            'description': 'Create your Email Reply Agent account and start automating your Gmail replies with AI. Free trial available - no credit card required.',
            'keywords': 'sign up, register, email automation, AI email assistant, Gmail integration, free trial',
            'og_title': 'Get Started with Email Reply Agent',
            'og_description': 'Join thousands of users who save hours every week with AI-powered email automation.',
            'twitter_title': 'Sign Up - Email Reply Agent',
            'twitter_description': 'Start your free trial of AI-powered email automation today.',
            'breadcrumbs': get_breadcrumbs('Sign Up')
        })
    elif page_type == 'dashboard':
        user_name = kwargs.get('user_name', 'User')
        seo_data.update({
            'title': f'Dashboard - {user_name} | Email Reply Agent',
            'description': 'Manage your AI-powered email automation, view generated replies, and control your email agent settings from your personalized dashboard.',
            'keywords': 'dashboard, email management, AI replies, email automation control, Gmail integration',
            'og_title': f'{user_name}\'s Email Agent Dashboard',
            'og_description': 'Control your AI-powered email automation and manage automated replies.',
            'twitter_title': 'Email Agent Dashboard',
            'twitter_description': 'Manage your automated email replies and AI settings.',
            'breadcrumbs': get_breadcrumbs('Dashboard')
        })
    elif page_type == 'email_preview':
        seo_data.update({
            'title': 'Email Preview - Email Reply Agent',
            'description': 'Preview, edit, and manage AI-generated email replies before sending. Review automated responses and maintain control over your email communications.',
            'keywords': 'email preview, AI replies, email management, automated responses, Gmail replies',
            'og_title': 'Email Preview and Management',
            'og_description': 'Review and manage your AI-generated email replies before sending.',
            'twitter_title': 'Email Preview - Email Reply Agent',
            'twitter_description': 'Preview and edit AI-generated email replies.',
            'breadcrumbs': get_breadcrumbs('Email Preview')
        })
    elif page_type == 'settings':
        seo_data.update({
            'title': 'Settings - Email Reply Agent',
            'description': 'Configure your Email Reply Agent settings, customize AI response preferences, and manage your email automation parameters.',
            'keywords': 'settings, email automation configuration, AI preferences, Gmail settings, email agent customization',
            'og_title': 'Email Agent Settings',
            'og_description': 'Customize your AI email automation preferences and settings.',
            'twitter_title': 'Settings - Email Reply Agent',
            'twitter_description': 'Configure your AI email automation settings.',
            'breadcrumbs': get_breadcrumbs('Settings')
        })
    elif page_type == 'profile':
        seo_data.update({
            'title': 'Profile - Email Reply Agent',
            'description': 'Manage your Email Reply Agent profile, account information, and subscription details.',
            'keywords': 'profile, account management, user settings, subscription, email agent account',
            'og_title': 'User Profile - Email Reply Agent',
            'og_description': 'Manage your account information and subscription details.',
            'twitter_title': 'Profile - Email Reply Agent',
            'twitter_description': 'Manage your Email Reply Agent account.',
            'breadcrumbs': get_breadcrumbs('Profile')
        })
    elif page_type == 'privacy':
        seo_data.update({
            'title': 'Privacy Policy - Email Reply Agent',
            'description': 'Learn how Email Reply Agent protects your privacy and handles your email data. Our commitment to data security and user privacy.',
            'keywords': 'privacy policy, data protection, email security, user privacy, data handling',
            'og_title': 'Privacy Policy - Email Reply Agent',
            'og_description': 'Our commitment to protecting your privacy and email data.',
            'twitter_title': 'Privacy Policy - Email Reply Agent',
            'twitter_description': 'Learn about our privacy and data protection practices.',
            'breadcrumbs': get_breadcrumbs('Privacy Policy')
        })
    elif page_type == 'terms':
        seo_data.update({
            'title': 'Terms of Service - Email Reply Agent',
            'description': 'Read the Terms of Service for Email Reply Agent. Understand your rights and responsibilities when using our AI-powered email automation service.',
            'keywords': 'terms of service, user agreement, service terms, email automation terms',
            'og_title': 'Terms of Service - Email Reply Agent',
            'og_description': 'Terms and conditions for using Email Reply Agent.',
            'twitter_title': 'Terms of Service - Email Reply Agent',
            'twitter_description': 'Terms and conditions for our email automation service.',
            'breadcrumbs': get_breadcrumbs('Terms of Service')
        })
    elif page_type == 'job_assistant':
        seo_data.update({
            'title': 'Job Assistant - Email Reply Agent',
            'description': 'Generate professional job application emails with AI. Upload your CV, add job details, and create personalized application emails instantly.',
            'keywords': 'job application, AI email generator, CV upload, job search, application emails, career assistant',
            'og_title': 'Job Assistant - AI-Powered Application Emails',
            'og_description': 'Create professional job application emails with AI assistance.',
            'twitter_title': 'Job Assistant - Email Reply Agent',
            'twitter_description': 'Generate personalized job application emails with AI.',
            'breadcrumbs': get_breadcrumbs('Job Assistant')
        })
    else:
        # Default SEO data
        seo_data.update({
            'title': 'Email Reply Agent - AI-Powered Email Automation',
            'description': 'Automate your Gmail replies with AI. Save time and increase productivity with intelligent email automation.',
            'keywords': 'email automation, AI email replies, Gmail automation, email productivity',
            'og_title': 'Email Reply Agent - AI Email Automation',
            'og_description': 'Intelligent email automation for Gmail users.',
            'twitter_title': 'Email Reply Agent',
            'twitter_description': 'AI-powered email automation for Gmail.',
            'breadcrumbs': get_breadcrumbs('Home')
        })

    return seo_data

# Allow OAuth over HTTP in development mode
# WARNING: This should NEVER be enabled in production
if not is_production():
    os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
    print("WARNING: OAuth insecure transport enabled for development")

# Configuration
OPENROUTER_API_KEY = os.getenv('OPENROUTER_API_KEY')
OPENROUTER_MODEL = config.OPENROUTER_MODEL
CHECK_INTERVAL_SECONDS = config.DEFAULT_CHECK_INTERVAL
REPLY_PREFIX = config.REPLY_PREFIX
SCOPES = config.GOOGLE_SCOPES
MAX_LOGS = config.MAX_LOGS

# Initialize OpenRouter Client
import httpx

# Create a custom HTTP client without proxies
http_client = httpx.Client()

client = OpenAI(
    base_url="https://openrouter.ai/api/v1",
    api_key=OPENROUTER_API_KEY,
    http_client=http_client
)

# Initialize Flask app
app = Flask(__name__)

# Initialize Flask-Compress for response compression
compress = Compress(app)

# Set Flask configuration from our config object
app.config['SECRET_KEY'] = config.SECRET_KEY
app.config['DEBUG'] = config.DEBUG
app.config['TESTING'] = getattr(config, 'TESTING', False)
app.config['DEVELOPMENT'] = not is_production()

# Get database URL from environment variable
database_url = os.getenv('DATABASE_URL', 'sqlite:///app.db')

# Add SSL mode to PostgreSQL connection if not already present
# Render PostgreSQL requires SSL connections
if database_url.startswith('postgresql://'):
    # Enhanced SSL configuration for PostgreSQL
    if '?' in database_url:
        if 'sslmode=' not in database_url:
            database_url += '&sslmode=require'
    else:
        database_url += '?sslmode=require'

    # Add additional SSL parameters to improve connection reliability
    if 'sslrootcert=' not in database_url:
        database_url += '&sslrootcert=none'

    print(f"Using PostgreSQL with SSL: {database_url.split('@')[0]}:****@{database_url.split('@')[1]}")

app.config['SQLALCHEMY_DATABASE_URI'] = database_url
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Add enhanced connection pool settings for PostgreSQL
if database_url.startswith('postgresql://'):
    app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
        'pool_size': config.DB_POOL_SIZE,
        'max_overflow': config.DB_MAX_OVERFLOW,
        'pool_timeout': config.DB_POOL_TIMEOUT,
        'pool_recycle': config.DB_POOL_RECYCLE,
        'pool_pre_ping': True,  # Enable connection health checks
        'connect_args': {
            'sslmode': 'require',
            'connect_timeout': 10,  # Increase connection timeout
            'application_name': 'email_agent'  # Identify application in database logs
        }
    }

# Set base URL from config
app.config['BASE_URL'] = config.BASE_URL

# Configure Flask-Compress for better performance
app.config['COMPRESS_MIMETYPES'] = [
    'text/html',
    'text/css',
    'text/xml',
    'text/javascript',
    'application/json',
    'application/javascript',
    'application/xml+rss',
    'application/atom+xml',
    'image/svg+xml'
]
app.config['COMPRESS_LEVEL'] = 6  # Good balance between compression ratio and speed
app.config['COMPRESS_MIN_SIZE'] = 500  # Only compress files larger than 500 bytes

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Initialize Flask-Migrate
migrate = Migrate(app, db)

# Add security and SEO headers
@app.after_request
def add_security_headers(response):
    """Add security and SEO-friendly headers to all responses"""
    # Security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    
    # Performance headers
    if response.mimetype.startswith('text/') or response.mimetype == 'application/javascript':
        response.headers['Cache-Control'] = 'public, max-age=31536000'  # 1 year for static assets
    elif response.mimetype.startswith('image/'):
        response.headers['Cache-Control'] = 'public, max-age=604800'  # 1 week for images
    else:
        response.headers['Cache-Control'] = 'public, max-age=3600'  # 1 hour for other content
    
    return response

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(user_id)

# Template context processors
@app.context_processor
def inject_seo_helpers():
    """Inject SEO helper functions into all templates"""
    return {
        'get_seo_data': get_seo_data,
        'get_canonical_url': get_canonical_url,
        'get_base_url': get_base_url
    }

# Create database tables and check connection
with app.app_context():
    try:
        # Check database connection using SQLAlchemy 2.0 API
        from sqlalchemy import text
        db.session.execute(text('SELECT 1'))
        db.session.commit()
        print("Database connection successful")

        # Create all tables
        db.create_all()
        print("Database tables created/verified successfully")
    except Exception as e:
        print(f"Database connection error: {e}")
        print("Warning: Application may not function correctly without database access")

# Initialize Rate Limiter with proper storage backend
limiter = Limiter(
    app=app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri="memory://"  # Use Redis URL for production: "redis://localhost:6379"
)

# Agent State
agent_running = False
agent_thread = None
agent_logs = []

# Helper Functions
def log_action(message):
    """Add a timestamped log entry"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)  # Print to console for debugging
    agent_logs.append(log_entry)
    if len(agent_logs) > MAX_LOGS:
        agent_logs.pop(0)

    # If the user is authenticated, also save to the database
    try:
        if current_user and current_user.is_authenticated:
            with app.app_context():
                # Use the User.log_action method instead of UserLog.create
                User.log_action(current_user.id, message)
    except Exception as e:
        print(f"Error saving log to database: {e}")

def get_gmail_service(user_id=None):
    """Build and return Gmail API service using the EmailAgent's method"""
    # If user_id is provided, use it
    if user_id:
        # Create a temporary agent to use its _get_gmail_service method
        temp_agent = EmailAgent(user_id, app=app)
        return temp_agent._get_gmail_service()
    # Otherwise try to get credentials from session (for backward compatibility)
    elif 'credentials' in session and current_user.is_authenticated:
        # Use the current user's agent
        if current_user.id not in agents:
            agents[current_user.id] = EmailAgent(current_user.id, app=app)
        return agents[current_user.id]._get_gmail_service()
    else:
        log_action("No credentials in session or user_id provided")
        return None

def fetch_unread_emails(user_id=None):
    """Fetch unread emails from Gmail using the EmailAgent's method"""
    user_id = user_id if user_id else current_user.id if current_user.is_authenticated else None

    if not user_id:
        log_action("No user ID available for fetching emails")
        return []

    # Create or get an agent for this user
    if user_id not in agents:
        agents[user_id] = EmailAgent(user_id, app=app)
    elif not agents[user_id].app:
        agents[user_id].app = app

    # Get the list of automated email addresses for this user
    with app.app_context():
        from models import AutomatedEmail, EmailReply
        automated_emails = AutomatedEmail.get_user_automated_emails(user_id) if user_id else []
        log_action(f"User has {len(automated_emails)} automated email addresses configured")

    # Fetch emails using the agent
    emails = agents[user_id]._fetch_unread_emails()

    # Filter out emails that already have replies
    with app.app_context():
        from models import EmailReply
        filtered_by_existing = []
        for email in emails:
            # Check if we already have a reply for this email
            existing_reply = EmailReply.get_reply_by_email_id(user_id, email['id'])
            if not existing_reply:
                filtered_by_existing.append(email)
            else:
                log_action(f"Skipping email with existing reply: {email['subject']}")

        emails = filtered_by_existing

    # Filter emails if automated emails are configured
    if automated_emails:
        filtered_emails = []
        for email in emails:
            sender_email = email['sender']
            if '<' in sender_email and '>' in sender_email:
                sender_email = sender_email.split('<')[1].split('>')[0]

            if sender_email in automated_emails:
                filtered_emails.append(email)
                log_action(f"Keeping email from {sender_email} - in automated emails list")
            else:
                log_action(f"Skipping email from {sender_email} - not in automated emails list")

        return filtered_emails

    return emails

def generate_reply(sender, subject, body, tone='professional', length='medium', custom_instructions=None):
    """Generate a reply using the EmailAgent's method"""
    user_id = current_user.id if current_user.is_authenticated else None

    if not user_id:
        log_action("No user ID available for generating reply")
        return None

    # Create or get an agent for this user
    if user_id not in agents:
        agents[user_id] = EmailAgent(user_id, app=app)
    elif not agents[user_id].app:
        agents[user_id].app = app

    # Generate reply using the agent
    reply = agents[user_id]._generate_reply(sender, subject, body, tone, length, custom_instructions)

    if reply:
        log_action(f"Reply generated: {reply[:50]}...")
    else:
        log_action("Failed to generate reply")

    return reply

def send_reply(email_id, to, subject, body, user_id=None):
    """Send a reply email using the EmailAgent's method"""
    user_id = user_id if user_id else current_user.id if current_user.is_authenticated else None

    if not user_id:
        log_action("No user ID available for sending reply")
        return False

    # Create or get an agent for this user
    if user_id not in agents:
        agents[user_id] = EmailAgent(user_id, app=app)
    elif not agents[user_id].app:
        agents[user_id].app = app

    # Send reply using the agent
    success = agents[user_id]._send_reply(email_id, to, subject, body)

    if success:
        log_action(f"Reply sent to {to}: {subject}")
        # Increment the replies sent count in the database
        try:
            from models import UserStats
            UserStats.increment_replies_sent(user_id)
        except Exception as e:
            print(f"Error incrementing replies sent count: {e}")
    else:
        log_action(f"Failed to send reply to {to}")

    return success

def mark_as_read(email_id, user_id=None):
    """Mark an email as read using the EmailAgent's method"""
    user_id = user_id if user_id else current_user.id if current_user.is_authenticated else None

    if not user_id:
        log_action("No user ID available for marking email as read")
        return False

    # Create or get an agent for this user
    if user_id not in agents:
        agents[user_id] = EmailAgent(user_id, app=app)
    elif not agents[user_id].app:
        agents[user_id].app = app

    # Mark as read using the agent
    success = agents[user_id]._mark_as_read(email_id)

    if success:
        log_action(f"Marked email {email_id} as read")
    else:
        log_action(f"Failed to mark email {email_id} as read")

    return success

# Agent Logic
def run_agent():
    """Background thread to check for and process emails"""
    global agent_running
    log_action("Agent started")

    # This function is kept for backward compatibility
    # The actual email processing is now handled by the EmailAgent class

    while agent_running:
        try:
            with app.app_context():
                # Get all users with enabled agents
                from models import UserSettings
                enabled_users = UserSettings.query.filter_by(agent_enabled=True).all()
                for user_settings in enabled_users:
                    user_id = user_settings.user_id

                    # Create or get an agent for this user if not already created
                    if user_id not in agents:
                        agents[user_id] = EmailAgent(user_id, app=app)
                    elif not agents[user_id].app:
                        agents[user_id].app = app

                    # Start the agent if it's not already running
                    if not agents[user_id].running:
                        success = agents[user_id].start()
                        if success:
                            log_action(f"Started email agent for user {user_id}")
                        else:
                            log_action(f"Failed to start email agent for user {user_id}")
        except Exception as e:
            log_action(f"Error in run_agent: {str(e)}")

        # Sleep for a while before checking again
        time.sleep(CHECK_INTERVAL_SECONDS)

    # Stop all running agents when the main agent is stopped
    for user_id, agent in agents.items():
        if agent.running:
            agent.stop()
            log_action(f"Stopped email agent for user {user_id}")

    log_action("Agent stopped")

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    seo_data = get_seo_data('landing')
    return render_template('landing.html', seo=seo_data)

@app.route('/login')
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    seo_data = get_seo_data('login')
    return render_template('login.html', seo=seo_data)

@app.route('/signup')
def signup():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    seo_data = get_seo_data('signup')
    return render_template('signup.html', seo=seo_data)

@app.route('/dashboard')
@login_required
def dashboard():
    user_settings = User.get_settings(current_user.id)
    user_logs = User.get_logs(current_user.id)
    user_name = current_user.name if hasattr(current_user, 'name') else current_user.email.split('@')[0]
    seo_data = get_seo_data('dashboard', user_name=user_name)
    return render_template('dashboard.html', settings=user_settings, logs=user_logs, seo=seo_data)

@app.route('/email_preview')
@login_required
def email_preview():
    """Email preview and management page"""
    seo_data = get_seo_data('email_preview')
    return render_template('email_preview.html', seo=seo_data)

@app.route('/profile')
@login_required
def profile():
    seo_data = get_seo_data('profile')
    return render_template('profile.html', seo=seo_data)

@app.route('/settings')
@login_required
def settings():
    user_settings = User.get_settings(current_user.id)
    seo_data = get_seo_data('settings')
    return render_template('settings.html', settings=user_settings, seo=seo_data)

@app.route('/job_assistant')
@login_required
def job_assistant():
    """Job Assistant page for generating job application emails"""
    seo_data = get_seo_data('job_assistant')
    return render_template('job_assistant.html', seo=seo_data)

@app.route('/api/job_assistant/upload_cv', methods=['POST'])
@login_required
def upload_cv():
    """Upload and parse CV/Resume"""
    try:
        if 'cv_file' not in request.files:
            return jsonify({'success': False, 'message': 'No file uploaded'})

        file = request.files['cv_file']
        if file.filename == '':
            return jsonify({'success': False, 'message': 'No file selected'})

        # Validate file type
        allowed_extensions = {'.pdf', '.docx'}
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            return jsonify({'success': False, 'message': 'Only PDF and DOCX files are allowed'})

        # Parse the file content
        try:
            cv_data = parse_cv_file(file, file_ext)
            cv_data['original_filename'] = secure_filename(file.filename)

            # Save to database
            cv_profile = CVProfile.create_profile(current_user.id, cv_data)

            # Parse JSON fields for frontend
            data = cv_profile.to_dict()  # This already parses JSON fields

            # Check for partial/incomplete extraction (e.g., missing name, email, or empty education/work_experience)
            required_fields = ['full_name', 'email', 'phone', 'address']
            array_fields = ['education', 'work_experience', 'skills', 'languages']
            missing = any(not data.get(f) for f in required_fields) or any(not data.get(f) for f in array_fields)
            if missing:
                return jsonify({
                    'success': False,
                    'message': 'Could not extract all information from your CV. Please re-upload a clearer file.',
                    'partial': True
                })

            return jsonify({
                'success': True,
                'message': 'CV uploaded and parsed successfully',
                'data': data
            })
        except ValueError as e:
            return jsonify({'success': False, 'message': str(e)})

    except Exception as e:
        logger.error(f"Error uploading CV: {str(e)}")
        return jsonify({'success': False, 'message': 'Failed to process CV file. Please try again.'})

def parse_cv_file(file, file_ext):
    """Parse CV file and extract information using AI"""
    try:
        # Extract text from file
        if file_ext == '.pdf':
            text = extract_text_from_pdf(file)
        elif file_ext == '.docx':
            text = extract_text_from_docx(file)
        else:
            raise ValueError("Unsupported file type")

        if not text.strip():
            raise ValueError("No text could be extracted from the file")

        # Use AI to parse the CV content
        cv_data = parse_cv_with_ai(text)
        return cv_data

    except ValueError as e:
        logger.error(f"Error parsing CV: {str(e)}")
        raise ValueError(str(e))
    except Exception as e:
        logger.error(f"Unexpected error parsing CV: {str(e)}")
        raise ValueError("Failed to process CV file. Please ensure the file is not corrupted and contains extractable text.")

def extract_text_from_pdf(file):
    """Extract text from PDF file"""
    temp_file = None
    try:
        # Save uploaded file to temporary location
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.close()  # Close the file handle before saving
        file.save(temp_file.name)
        logger.info(f"Saved PDF to temporary file: {temp_file.name}")

        # Read PDF content
        with open(temp_file.name, 'rb') as pdf_file:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            if len(pdf_reader.pages) == 0:
                raise ValueError("PDF file appears to be empty or corrupted")
            
            text = ""
            for i, page in enumerate(pdf_reader.pages):
                try:
                    page_text = page.extract_text()
                    if not page_text:
                        # Try to get raw text if extract_text() fails
                        page_text = page.get_text()
                        if not page_text:
                            logger.warning(f"Page {i+1} appears to be empty or contains no extractable text")
                    text += page_text + "\n"
                except Exception as page_error:
                    logger.warning(f"Error extracting text from page {i+1}: {str(page_error)}")
                    continue

            if not text.strip():
                # If no text was extracted, try using pdfplumber as a fallback
                try:
                    import pdfplumber
                    with pdfplumber.open(temp_file.name) as pdf:
                        text = ""
                        for page in pdf.pages:
                            page_text = page.extract_text()
                            if page_text:
                                text += page_text + "\n"
                except ImportError:
                    logger.warning("pdfplumber not installed, skipping fallback extraction")
                except Exception as plumber_error:
                    logger.warning(f"Error using pdfplumber: {str(plumber_error)}")

            if not text.strip():
                raise ValueError("No text could be extracted from the PDF. The file might be scanned or contain only images.")

        logger.info("Successfully extracted text from PDF")
        return text

    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")
        raise ValueError(f"Failed to extract text from PDF: {str(e)}")

    finally:
        # Clean up temp file in finally block to ensure it runs
        if temp_file and os.path.exists(temp_file.name):
            try:
                os.unlink(temp_file.name)
            except Exception as cleanup_error:
                logger.warning(f"Failed to delete temporary file {temp_file.name}: {str(cleanup_error)}")

def extract_text_from_docx(file):
    """Extract text from DOCX file"""
    try:
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
            file.save(temp_file.name)

            # Read DOCX content
            doc = docx.Document(temp_file.name)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"

            # Clean up temp file
            os.unlink(temp_file.name)
            return text

    except Exception as e:
        print(f"Error extracting text from DOCX: {e}")
        return ""

        
import json
import logging
import sys
import re
import time

# Configure logging for console output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def parse_cv_with_ai(text):
    """Use AI to parse CV content and extract structured information"""
    max_tokens = max(4000, len(text) // 2)  # Adjust based on text length
    retries = 3
    for attempt in range(retries):
        try:
            prompt = f"""
            Please analyze the following CV/Resume text and extract structured information in JSON format.
            Extract the following fields:
            - full_name: The person's full name (string)
            - email: Email address (string)
            - phone: Phone number (string)
            - address: Full address (string)
            - education: Array of education entries (degree, institution, year, etc.) (array)
            - work_experience: Array of work experience entries (position, company, duration, description) (array)
            - skills: Array of skills (array)
            - languages: Array of languages spoken (array)
            - additional_info: Any other relevant information (string)

            CV Text:
            {text}
            Ensure the JSON response is complete, properly formatted, and includes all requested fields, even if empty (e.g., [] for arrays, "" for strings). Do not truncate the response.
            """

            response = client.chat.completions.create(
                model=OPENROUTER_MODEL,
                messages=[
                    {"role": "system", "content": "You are a CV parsing assistant. Extract information from CVs and return structured JSON data. Always return complete, valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=max_tokens
            )

            # Parse the AI response
            ai_response = response.choices[0].message.content.strip()
            logger.info("AI Response: %s", ai_response)

            # Extract JSON from response - handle cases where JSON is wrapped in markdown blocks
            json_content = ai_response
            
            # Look for JSON block markers
            if '```json' in ai_response:
                # Extract content between ```json and ```
                start_marker = ai_response.find('```json')
                if start_marker != -1:
                    json_start = start_marker + 7  # Length of '```json'
                    end_marker = ai_response.find('```', json_start)
                    if end_marker != -1:
                        json_content = ai_response[json_start:end_marker]
                    else:
                        json_content = ai_response[json_start:]
            elif '```' in ai_response:
                # Extract content between ``` markers (assuming JSON)
                parts = ai_response.split('```')
                if len(parts) >= 3:
                    json_content = parts[1]  # Content between first pair of ```
                elif len(parts) == 2:
                    json_content = parts[1]  # Content after first ```
            
            # Clean up the extracted JSON content
            json_content = json_content.strip()

            # Attempt to fix incomplete JSON
            if not json_content.endswith('}'):
                logger.warning("Incomplete JSON response detected, attempting to fix...")
                json_content = fix_incomplete_json(json_content)

            # Parse JSON
            try:
                cv_data = json.loads(json_content)

                # Ensure all required fields exist
                required_fields = ['full_name', 'email', 'phone', 'address', 'education', 'work_experience', 'skills', 'languages', 'additional_info']
                for field in required_fields:
                    if field not in cv_data:
                        cv_data[field] = [] if field in ['education', 'work_experience', 'skills', 'languages'] else ''

                # Convert arrays to JSON strings for database storage
                for field in ['education', 'work_experience', 'skills', 'languages']:
                    if isinstance(cv_data[field], list):
                        cv_data[field] = json.dumps(cv_data[field])

                return cv_data

            except json.JSONDecodeError as e:
                logger.error("Error parsing AI response as JSON: %s", e)
                logger.error("Extracted JSON Content: %s", json_content)
                # Attempt partial recovery
                partial_data = attempt_partial_json_parse(json_content)
                if partial_data:
                    logger.info("Successfully recovered partial JSON data")
                    return partial_data
                if attempt < retries - 1:
                    logger.warning(f"Retrying (attempt {attempt + 2}/{retries})...")
                    max_tokens += 1000  # Increase tokens for retry
                    time.sleep(1)  # Brief delay to avoid rate limits
                    continue
                # Fallback to default structure
                return {
                    'full_name': '',
                    'email': '',
                    'phone': '',
                    'address': '',
                    'education': json.dumps([]),
                    'work_experience': json.dumps([]),
                    'skills': json.dumps([]),
                    'languages': json.dumps([]),
                    'additional_info': ''
                }

        except Exception as e:
            logger.error("Error parsing CV with AI: %s", e)
            if attempt < retries - 1:
                logger.warning(f"Retrying (attempt {attempt + 2}/{retries})...")
                time.sleep(1)
                continue
            # Return fallback structure
            return {
                'full_name': '',
                'email': '',
                'phone': '',
                'address': '',
                'education': json.dumps([]),
                'work_experience': json.dumps([]),
                'skills': json.dumps([]),
                'languages': json.dumps([]),
                'additional_info': ''
            }

def fix_incomplete_json(json_str):
    """Attempt to fix incomplete JSON by closing open objects and arrays"""
    try:
        # Remove trailing commas and incomplete key-value pairs
        json_str = re.sub(r',\s*([\]}])', r'\1', json_str)
        json_str = re.sub(r'"\w*"\s*:\s*[^,}\]]*,?\s*$', '', json_str)

        # Count open and close braces/brackets
        open_braces = json_str.count('{')
        close_braces = json_str.count('}')
        open_brackets = json_str.count('[')
        close_brackets = json_str.count(']')

        # Add missing closing braces and brackets
        if open_braces > close_braces:
            json_str += '}' * (open_braces - close_braces)
        if open_brackets > close_brackets:
            json_str += ']' * (open_brackets - close_brackets)

        # If truncated at education field, add default empty array
        if json_str.rstrip('}').endswith('"education":'):
            json_str = json_str.rstrip('}') + '"education": []}'

        return json_str
    except Exception as e:
        logger.warning("Failed to fix incomplete JSON: %s", e)
        return json_str

def attempt_partial_json_parse(json_str):
    """Attempt to parse partial JSON and recover usable data"""
    try:
        # Try parsing progressively smaller portions
        last_valid_pos = len(json_str)
        while last_valid_pos > 0:
            try:
                partial_json = json_str[:last_valid_pos]
                if partial_json.endswith('}') or partial_json.endswith(']'):
                    data = json.loads(partial_json)
                    if isinstance(data, dict):
                        # Ensure required fields
                        required_fields = ['full_name', 'email', 'phone', 'address', 'education', 'work_experience', 'skills', 'languages', 'additional_info']
                        for field in required_fields:
                            if field not in data:
                                data[field] = [] if field in ['education', 'work_experience', 'skills', 'languages'] else ''
                        # Convert arrays to JSON strings
                        for field in ['education', 'work_experience', 'skills', 'languages']:
                            if isinstance(data[field], list):
                                data[field] = json.dumps(data[field])
                        return data
            except json.JSONDecodeError:
                pass
            last_valid_pos -= 1
        # If partial parsing fails, try regex-based extraction
        return extract_partial_data_with_regex(json_str)
    except Exception as e:
        logger.warning("Failed to recover partial JSON: %s", e)
        return None

def extract_partial_data_with_regex(json_str):
    """Extract partial data using regex when JSON parsing fails"""
    try:
        default_data = {
            'full_name': '',
            'email': '',
            'phone': '',
            'address': '',
            'education': json.dumps([]),
            'work_experience': json.dumps([]),
            'skills': json.dumps([]),
            'languages': json.dumps([]),
            'additional_info': ''
        }
        # Extract fields using regex
        full_name_match = re.search(r'"full_name":\s*"([^"]*)"', json_str)
        email_match = re.search(r'"email":\s*"([^"]*)"', json_str)
        phone_match = re.search(r'"phone":\s*"([^"]*)"', json_str)
        address_match = re.search(r'"address":\s*"([^"]*)"', json_str)

        if full_name_match:
            default_data['full_name'] = full_name_match.group(1)
        if email_match:
            default_data['email'] = email_match.group(1)
        if phone_match:
            default_data['phone'] = phone_match.group(1)
        if address_match:
            default_data['address'] = address_match.group(1)

        return default_data if any(default_data[field] for field in ['full_name', 'email', 'phone', 'address']) else None
    except Exception as e:
        logger.warning("Failed to extract partial data with regex: %s", e)
        return None
@app.route('/api/job_assistant/analyze_job', methods=['POST'])
@login_required
def analyze_job():
    """Analyze job description and provide insights"""
    try:
        data = request.get_json()
        job_description = data.get('job_description', '')

        if not job_description:
            return jsonify({'success': False, 'message': 'Job description is required'})

        # Use AI to analyze the job description
        analysis = analyze_job_with_ai(job_description)

        return jsonify({
            'success': True,
            'data': analysis
        })

    except Exception as e:
        print(f"Error analyzing job: {e}")
        return jsonify({'success': False, 'message': 'Failed to analyze job description'})

def analyze_job_with_ai(job_description):
    """Use AI to analyze job description and extract key information"""
    try:
        prompt = f"""
        Analyze the following job description and extract key information in JSON format:

        Job Description:
        {job_description}

        Please provide:
        - requirements: Array of key requirements/qualifications
        - skills_to_highlight: Array of skills that should be highlighted in application
        - company_culture: Brief description of company culture if mentioned
        - key_responsibilities: Array of main responsibilities

        Respond with only valid JSON format.
        """

        response = client.chat.completions.create(
            model=OPENROUTER_MODEL,
            messages=[
                {"role": "system", "content": "You are a job analysis assistant. Analyze job descriptions and provide structured insights."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1
        )

        ai_response = response.choices[0].message.content.strip()

        # Extract JSON from response - handle cases where JSON is wrapped in markdown blocks
        json_content = ai_response
        
        # Look for JSON block markers
        if '```json' in ai_response:
            # Extract content between ```json and ```
            start_marker = ai_response.find('```json')
            if start_marker != -1:
                json_start = start_marker + 7  # Length of '```json'
                end_marker = ai_response.find('```', json_start)
                if end_marker != -1:
                    json_content = ai_response[json_start:end_marker]
                else:
                    json_content = ai_response[json_start:]
        elif '```' in ai_response:
            # Extract content between ``` markers (assuming JSON)
            parts = ai_response.split('```')
            if len(parts) >= 3:
                json_content = parts[1]  # Content between first pair of ```
            elif len(parts) == 2:
                json_content = parts[1]  # Content after first ```
        
        # Clean up the extracted JSON content
        json_content = json_content.strip()

        analysis = json.loads(json_content)
        return analysis

    except Exception as e:
        print(f"Error analyzing job with AI: {e}")
        return {
            'requirements': ['Unable to analyze requirements'],
            'skills_to_highlight': ['Review job description manually'],
            'company_culture': 'Not specified',
            'key_responsibilities': ['Unable to extract responsibilities']
        }

@app.route('/api/job_assistant/generate_email', methods=['POST'])
@login_required
def generate_job_email():
    """Generate job application email"""
    try:
        data = request.get_json()
        cv_data = data.get('cv_data', {})
        application_data = data.get('application_data', {})

        # Create job application record
        job_app = JobApplication.create_application(current_user.id, application_data)

        # Generate email using AI
        email_content = generate_job_email_with_ai(cv_data, application_data)

        # Create generated email record
        email_data = {
            'subject': email_content['subject'],
            'body': email_content['body'],
            'signature': email_content['signature']
        }

        generated_email = GeneratedJobEmail.create_email(
            current_user.id,
            job_app.id,
            email_data
        )

        return jsonify({
            'success': True,
            'data': {
                'id': generated_email.id,
                'subject': generated_email.subject,
                'body': generated_email.body,
                'signature': generated_email.signature
            }
        })

    except Exception as e:
        print(f"Error generating job email: {e}")
        return jsonify({'success': False, 'message': 'Failed to generate email'})

def generate_job_email_with_ai(cv_data, application_data):
    """Use AI to generate personalized job application email"""
    try:
        # Prepare the prompt with all available data
        prompt = f"""
        Generate a professional job application email based on the following information:

        APPLICANT INFORMATION:
        - Name: {cv_data.get('full_name', '')}
        - Email: {cv_data.get('email', '')}
        - Phone: {cv_data.get('phone', '')}
        - Education: {cv_data.get('education', '')}
        - Work Experience: {cv_data.get('work_experience', '')}
        - Skills: {cv_data.get('skills', '')}
        - Additional Info: {cv_data.get('additional_info', '')}

        JOB INFORMATION:
        - Company: {application_data.get('company_name', '')}
        - Position: {application_data.get('position_title', '')}
        - Job Description: {application_data.get('job_description', '')}
        - HR Contact: {application_data.get('hr_name', 'Hiring Manager')}

        EMAIL PREFERENCES:
        - Tone: {application_data.get('email_tone', 'professional')}
        - Language: {application_data.get('email_language', 'english')}
        - Type: {application_data.get('email_type', 'application')}

        Please generate:
        1. A compelling subject line
        2. A personalized email body that:
           - Uses the specified tone
           - Highlights relevant experience and skills
           - Shows enthusiasm for the role
           - Mentions specific qualifications that match the job
           - Includes a call to action
        3. A professional signature

        Format the response as JSON with keys: subject, body, signature
        """

        response = client.chat.completions.create(
            model=OPENROUTER_MODEL,
            messages=[
                {"role": "system", "content": "You are a professional email writing assistant specializing in job applications. Create compelling, personalized application emails."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3
        )

        ai_response = response.choices[0].message.content.strip()

        # Extract JSON from response - handle cases where JSON is wrapped in markdown blocks
        json_content = ai_response
        
        # Look for JSON block markers
        if '```json' in ai_response:
            # Extract content between ```json and ```
            start_marker = ai_response.find('```json')
            if start_marker != -1:
                json_start = start_marker + 7  # Length of '```json'
                end_marker = ai_response.find('```', json_start)
                if end_marker != -1:
                    json_content = ai_response[json_start:end_marker]
                else:
                    json_content = ai_response[json_start:]
        elif '```' in ai_response:
            # Extract content between ``` markers (assuming JSON)
            parts = ai_response.split('```')
            if len(parts) >= 3:
                json_content = parts[1]  # Content between first pair of ```
            elif len(parts) == 2:
                json_content = parts[1]  # Content after first ```
        
        # Clean up the extracted JSON content
        json_content = json_content.strip()

        email_content = json.loads(json_content)

        # Ensure all required fields exist
        if 'subject' not in email_content:
            email_content['subject'] = f"Application for {application_data.get('position_title', 'Position')} at {application_data.get('company_name', 'Company')}"

        if 'body' not in email_content:
            email_content['body'] = "I am writing to express my interest in this position. Please find my qualifications outlined in my attached resume."

        if 'signature' not in email_content:
            email_content['signature'] = f"Best regards,\n{cv_data.get('full_name', 'Applicant')}\n{cv_data.get('email', '')}\n{cv_data.get('phone', '')}"

        return email_content

    except Exception as e:
        print(f"Error generating email with AI: {e}")
        # Return fallback email content
        return {
            'subject': f"Application for {application_data.get('position_title', 'Position')} at {application_data.get('company_name', 'Company')}",
            'body': f"Dear {application_data.get('hr_name', 'Hiring Manager')},\n\nI am writing to express my strong interest in the {application_data.get('position_title', 'position')} role at {application_data.get('company_name', 'your company')}.\n\nWith my background and experience, I believe I would be a valuable addition to your team. I would welcome the opportunity to discuss how my skills can contribute to your organization's success.\n\nThank you for your consideration. I look forward to hearing from you.\n\nSincerely,",
            'signature': f"{cv_data.get('full_name', 'Applicant')}\n{cv_data.get('email', '')}\n{cv_data.get('phone', '')}"
        }

@app.route('/api/job_assistant/regenerate_email/<int:email_id>', methods=['POST'])
@login_required
def regenerate_job_email(email_id):
    """Regenerate job application email with modifications"""
    try:
        data = request.get_json()
        modified_body = data.get('modified_body', '')

        # Get the existing email
        existing_email = GeneratedJobEmail.get_email(email_id, current_user.id)
        if not existing_email:
            return jsonify({'success': False, 'message': 'Email not found'})

        # Get the job application
        job_app = JobApplication.query.get(existing_email.job_application_id)
        if not job_app:
            return jsonify({'success': False, 'message': 'Job application not found'})

        # Get CV data
        cv_profile = CVProfile.get_active_profile(current_user.id)
        if not cv_profile:
            return jsonify({'success': False, 'message': 'CV profile not found'})

        # Prepare application data
        application_data = job_app.to_dict()
        cv_data = cv_profile.to_dict()

        # Generate new email content
        email_content = generate_job_email_with_ai(cv_data, application_data)

        # Update the existing email
        existing_email.subject = email_content['subject']
        existing_email.body = email_content['body']
        existing_email.signature = email_content['signature']
        existing_email.modified_body = modified_body
        db.session.commit()

        return jsonify({
            'success': True,
            'data': {
                'id': existing_email.id,
                'subject': existing_email.subject,
                'body': existing_email.body,
                'signature': existing_email.signature
            }
        })

    except Exception as e:
        print(f"Error regenerating email: {e}")
        return jsonify({'success': False, 'message': 'Failed to regenerate email'})

@app.route('/api/job_assistant/send_email/<int:email_id>', methods=['POST'])
@login_required
def send_job_email(email_id):
    """Send job application email"""
    try:
        data = request.get_json()
        modified_body = data.get('modified_body', '')

        # Get the email
        email_record = GeneratedJobEmail.get_email(email_id, current_user.id)
        if not email_record:
            return jsonify({'success': False, 'message': 'Email not found'})

        # Get the job application
        job_app = JobApplication.query.get(email_record.job_application_id)
        if not job_app:
            return jsonify({'success': False, 'message': 'Job application not found'})

        # Update email with any modifications
        if modified_body:
            email_record.modified_body = modified_body

        # Prepare email content
        email_body = modified_body if modified_body else email_record.body
        full_email_content = f"{email_body}\n\n{email_record.signature}"

        # Send the email using the existing email sending functionality
        success = send_reply(
            email_id=None,  # Not a reply to an existing email
            to=job_app.hr_email,
            subject=email_record.subject,
            body=full_email_content,
            user_id=current_user.id
        )

        if success:
            # Mark email as sent
            email_record.mark_as_sent()

            # Update job application status
            job_app.status = 'sent'
            db.session.commit()

            # Update user stats
            from models import UserStats
            UserStats.increment_job_applications_sent(current_user.id)

            # Log the action
            log_action(f"Job application sent to {job_app.hr_email} for {job_app.position_title} at {job_app.company_name}")

            return jsonify({
                'success': True,
                'message': 'Application email sent successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to send email. Please check your email settings.'
            })

    except Exception as e:
        print(f"Error sending job email: {e}")
        return jsonify({'success': False, 'message': 'Failed to send email'})

@app.route('/api/job_assistant/history', methods=['GET'])
@login_required
def get_job_application_history():
    """Get user's job application history"""
    try:
        applications = JobApplication.get_user_applications(current_user.id)

        history = []
        for app in applications:
            app_data = app.to_dict()

            # Get associated emails
            emails = GeneratedJobEmail.query.filter_by(
                user_id=current_user.id,
                job_application_id=app.id
            ).all()

            app_data['emails'] = [email.to_dict() for email in emails]
            history.append(app_data)

        return jsonify({
            'success': True,
            'data': history
        })

    except Exception as e:
        print(f"Error getting job application history: {e}")
        return jsonify({'success': False, 'message': 'Failed to get application history'})

@app.route('/logout')
@login_required
def logout():
    logout_user()
    session.clear()
    return redirect(url_for('index'))

def get_oauth_config():
    """Get OAuth configuration from environment variables"""
    client_config = {
        'web': {
            'client_id': os.getenv('GOOGLE_CLIENT_ID'),
            'client_secret': os.getenv('GOOGLE_CLIENT_SECRET'),
            'auth_uri': 'https://accounts.google.com/o/oauth2/auth',
            'token_uri': 'https://oauth2.googleapis.com/token',
            'auth_provider_x509_cert_url': 'https://www.googleapis.com/oauth2/v1/certs',
        }
    }

    if not client_config['web']['client_id'] or not client_config['web']['client_secret']:
        raise ValueError('GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET must be set')

    return client_config

@app.route('/auth')
def auth():
    # Get the base URL from app config (which we've already set correctly)
    base_url = app.config['BASE_URL']

    # Construct the callback URI using the base URL
    callback_uri = f"{base_url}/oauth2callback"

    # Double-check HTTPS in production
    if is_production() and callback_uri.startswith('http://'):
        callback_uri = callback_uri.replace('http://', 'https://')
        print(f'Warning: Had to convert callback URI to HTTPS: {callback_uri}')

    print(f'Using redirect URI: {callback_uri}')

    # Store the callback URI in session for consistency
    session['redirect_uri'] = callback_uri

    # Get OAuth config from environment
    client_config = get_oauth_config()

    # Add the redirect URI to the config
    client_config['web']['redirect_uris'] = [callback_uri]

    flow = Flow.from_client_config(
        client_config,
        scopes=SCOPES,
        redirect_uri=callback_uri
    )

    authorization_url, state = flow.authorization_url(
        access_type='offline',
        include_granted_scopes='true',
        prompt='consent'  # Force consent screen to ensure refresh token
    )

    session['state'] = state
    return redirect(authorization_url)

@app.route('/oauth2callback')
def oauth2callback():
    # Get state from the request
    request_state = request.args.get('state', '')

    # Check if state exists in session
    if 'state' not in session:
        flash('Authentication flow was not properly initiated. Please try again.', 'error')
        return redirect(url_for('login'))

    # Get state from session
    session_state = session['state']

    # Get the redirect URI from session
    callback_uri = session.get('redirect_uri')
    if not callback_uri:
        # Fallback to constructing from base URL if not in session
        base_url = app.config['BASE_URL']  # Use the app config which we've already set correctly
        callback_uri = f"{base_url}/oauth2callback"

        # Double-check HTTPS in production
        if is_production() and callback_uri.startswith('http://'):
            callback_uri = callback_uri.replace('http://', 'https://')
            print(f'Warning: Had to convert callback URI to HTTPS: {callback_uri}')

    print(f'Callback using redirect URI: {callback_uri}')

    try:
        # Get OAuth config from environment
        client_config = get_oauth_config()

        # Add the redirect URI to the config
        client_config['web']['redirect_uris'] = [callback_uri]

        flow = Flow.from_client_config(
            client_config,
            scopes=SCOPES,
            state=session_state,
            redirect_uri=callback_uri
        )

        # Make sure we're using the full URL with the correct scheme
        authorization_response = request.url

        # Use the state from the request for token fetch
        flow.state = request_state
        flow.fetch_token(authorization_response=authorization_response)
    except Exception as e:
        flash(f'Authentication error: {str(e)}', 'error')
        print(f'OAuth error: {str(e)}')
        return redirect(url_for('login'))
    credentials = flow.credentials

    oauth2_client = build('oauth2', 'v2', credentials=credentials)
    user_info = oauth2_client.userinfo().get().execute()

    user = User.create_or_update(
        google_id=user_info['id'],
        email=user_info['email'],
        name=user_info['name'],
        picture=user_info.get('picture'),
        token_info={
            'token': credentials.token,
            'refresh_token': credentials.refresh_token,
            'token_uri': credentials.token_uri,
            'client_id': credentials.client_id,
            'client_secret': credentials.client_secret,
            'scopes': credentials.scopes,
            'expiry': credentials.expiry.isoformat() if credentials.expiry else None
        }
    )

    login_user(user)
    return redirect(url_for('dashboard'))

@app.route('/api/settings', methods=['POST'])
@login_required
def update_settings():
    data = request.get_json()
    User.update_settings(current_user.id, data)
    return jsonify({'status': 'success'})

@app.route('/api/start_agent', methods=['POST'])
@login_required
def start_agent_endpoint():
    user_settings = User.get_settings(current_user.id)
    if not user_settings['agent_enabled']:
        return jsonify({'status': 'error', 'message': 'Agent is disabled'})

    thread = threading.Thread(target=start_agent, args=(current_user.id,))
    thread.daemon = True
    thread.start()
    return jsonify({'status': 'success'})

@app.route('/api/stop_agent', methods=['POST'])
@login_required
def stop_agent_endpoint():
    # Implement agent stopping logic here
    return jsonify({'status': 'success'})

@app.route('/status', methods=['GET'])
@limiter.exempt  # Exempt this route from rate limiting
def get_status():
    """Get agent status"""
    # Check if the agent thread is alive
    global agent_running, agent_thread

    # Store the last status and timestamp in function attributes for persistence between calls
    if not hasattr(get_status, 'last_stable_status'):
        get_status.last_stable_status = None
        get_status.last_stable_timestamp = 0
        get_status.last_stopped_time = None
        get_status.state_change_count = 0
        get_status.last_state = None
        get_status.stability_counter = 0

    # Check if the current user has an agent running
    user_agent_running = False
    user_thread_alive = False
    if current_user.is_authenticated and current_user.id in agents:
        user_agent = agents[current_user.id]
        user_agent_running = user_agent.running
        user_thread_alive = user_agent.thread is not None and user_agent.thread.is_alive()

    # If agent_thread exists and is alive, ensure agent_running is True
    global_thread_alive = agent_thread is not None and agent_thread.is_alive()
    if global_thread_alive:
        agent_running = True
        # Reset last stopped time when agent is confirmed running
        get_status.last_stopped_time = None
    # If agent_thread doesn't exist or is not alive but agent_running is True, set it to False
    elif agent_running and not global_thread_alive:
        agent_running = False
        # Record when the agent was stopped
        if get_status.last_stopped_time is None:
            get_status.last_stopped_time = time.time()

    # Determine the actual status based on both the global agent and the user's agent
    # If the agent was recently stopped (within 5 seconds), keep it as stopped to prevent fluctuation
    recently_stopped = get_status.last_stopped_time is not None and (time.time() - get_status.last_stopped_time < 5)

    # Only consider the agent running if it's actually running and not recently stopped
    actual_running = (agent_running or user_agent_running) and not recently_stopped
    
    # Get current timestamp for stability calculations
    now = time.time()
    
    # Detect state changes
    if get_status.last_state is not None and get_status.last_state != actual_running:
        get_status.state_change_count += 1
        get_status.stability_counter = 0
        
        # Log state changes if there are too many (may indicate a problem)
        if get_status.state_change_count > 3:
            log_action(f"Warning: Agent state fluctuating ({get_status.state_change_count} changes in short period)")
    else:
        # State is consistent with last check, increment stability counter
        get_status.stability_counter += 1
        
        # If state has been stable for several checks (at least 3), reset change counter
        if get_status.stability_counter >= 3:
            if get_status.state_change_count > 0:
                log_action(f"Agent state has stabilized at {'running' if actual_running else 'stopped'}")
            get_status.state_change_count = 0
    
    # Update last state for next comparison
    get_status.last_state = actual_running
    
    # Implement stability tracking logic - only update stable status after consistent readings
    if get_status.last_stable_status is None:
        # First time initialization
        get_status.last_stable_status = actual_running
        get_status.last_stable_timestamp = now
    elif get_status.last_stable_status != actual_running:
        # State differs from last stable state
        # Only update stable state after a minimum period of stability
        if now - get_status.last_stable_timestamp >= 5.0:  # 5 second stability threshold
            get_status.last_stable_status = actual_running
            get_status.last_stable_timestamp = now
            log_action(f"Agent stable state changed to: {'running' if actual_running else 'stopped'}")
    else:
        # State is consistent with last stable state, update timestamp
        get_status.last_stable_timestamp = now
    
    # Use the stabilized state for excessive fluctuations
    reported_running = actual_running
    if get_status.state_change_count > 3:
        # If there's too much fluctuation, use the last stable state
        reported_running = get_status.last_stable_status
        log_action(f"Using stable state due to fluctuation: {'running' if reported_running else 'stopped'}")

    # Get more detailed status information
    status_details = {
        'global_agent': agent_running and not recently_stopped,
        'user_agent': user_agent_running and not recently_stopped,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'thread_alive': global_thread_alive or user_thread_alive,
        'recently_stopped': recently_stopped,
        'production_mode': is_production(),
        'fluctuation_count': get_status.state_change_count,
        'stability_score': get_status.stability_counter
    }

    # Log status changes in production mode to help with debugging
    if is_production() and hasattr(get_status, 'last_status') and get_status.last_status != reported_running:
        log_action(f"Agent status changed: {get_status.last_status} -> {reported_running} (Details: {status_details})")

    # Store the current status for next comparison
    get_status.last_status = reported_running

    return jsonify({
        'running': reported_running,
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'thread_alive': global_thread_alive or user_thread_alive,
        'details': status_details
    })

@app.route('/logs', methods=['GET'])
@limiter.exempt  # Exempt this route from rate limiting
def get_logs():
    """Get agent logs"""
    # Add a log entry to help debug but don't add it to the response
    log_message = "Logs requested"
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {log_message}")

    # Get database logs if user is authenticated
    db_logs = []
    if current_user.is_authenticated:
        try:
            user_logs = User.get_logs(current_user.id, limit=100)
            for log in user_logs:
                timestamp = log['timestamp'].strftime('%Y-%m-%d %H:%M:%S') if hasattr(log['timestamp'], 'strftime') else log['timestamp']
                db_logs.append(f"[{timestamp}] {log['message']}")
        except Exception as e:
            print(f"Error getting logs from database: {e}")

    # Combine memory logs and database logs, removing duplicates
    all_logs = list(agent_logs)
    for log in db_logs:
        if log not in all_logs:
            all_logs.append(log)

    # Sort logs by timestamp (assuming format [YYYY-MM-DD HH:MM:SS])
    all_logs.sort(key=lambda x: x.split(']')[0] + ']' if ']' in x else x, reverse=True)

    # Filter logs to only show important ones based on config
    important_logs = []
    for log in all_logs:
        # Always include error logs
        if 'Error' in log or 'error' in log:
            important_logs.append(log)
            continue

        # Check if log contains any of the important log types
        is_important = False
        for log_type in config.IMPORTANT_LOG_TYPES:
            if log_type in log:
                is_important = True
                break

        if is_important:
            important_logs.append(log)

    # Limit the number of logs to return
    important_logs = important_logs[:config.MAX_LOGS]

    # Get stats from database if user is authenticated
    stats = {
        'emails_processed': 0,
        'replies_sent': 0
    }

    if current_user.is_authenticated:
        try:
            from models import UserStats
            user_stats = UserStats.get_stats(current_user.id)
            stats = user_stats
        except Exception as e:
            print(f"Error getting stats from database: {e}")

            # Fallback to counting from logs if database stats fail
            emails_processed = 0
            replies_sent = 0

            # Count all emails processed from logs
            for log in all_logs:  # Use all_logs for stats to ensure accuracy
                if 'Found' in log and 'unread emails' in log:
                    # Extract number from log like "Found 5 unread emails"
                    match = re.search(r'Found (\d+) unread emails', log)
                    if match:
                        emails_processed += int(match.group(1))  # Add to total count, not just replace

                # Count all replies sent
                if 'Reply sent to' in log or 'Sent email reply' in log:
                    replies_sent += 1

            stats = {
                'emails_processed': emails_processed,
                'replies_sent': replies_sent
            }

    return jsonify({
        'logs': important_logs,
        'stats': stats
    })

@app.route('/start', methods=['POST'])
def start_agent():
    """Start the agent"""
    global agent_running, agent_thread

    # Check if user is authenticated
    if not current_user.is_authenticated:
        return jsonify({'status': 'Error: Not authenticated. Please sign in with Google.'})

    # Get user's token from database
    user_token = OAuthToken.query.filter_by(user_id=current_user.id).first()
    if not user_token or not user_token.token_info:
        return jsonify({'status': 'Error: No valid credentials found. Please re-authenticate.'})

    # Check if the token is valid
    try:
        # Try to get credentials from the token
        credentials = user_token.get_credentials()
        if not credentials:
            log_action(f"Invalid token format for user {current_user.id}")
            return jsonify({'status': 'Error: Invalid token format. Please re-authenticate.'})

        # Check if required fields are present
        required_fields = ['token', 'refresh_token', 'token_uri', 'client_id', 'client_secret']
        missing_fields = [field for field in required_fields if field not in credentials]
        if missing_fields:
            log_action(f"Token missing required fields: {missing_fields}")
            return jsonify({'status': f'Error: Token missing required fields: {missing_fields}. Please re-authenticate.'})
    except Exception as e:
        log_action(f"Error checking token: {str(e)}")
        return jsonify({'status': f'Error checking token: {str(e)}. Please re-authenticate.'})

    # Get or create user settings
    user_settings = User.get_settings(current_user.id)
    if not user_settings:
        # Create default settings for this user
        log_action(f"Creating default settings for user {current_user.id}")
        default_settings = {
            'agent_enabled': True,
            'check_interval': config.DEFAULT_CHECK_INTERVAL,
            'custom_signature': f"Best regards,\n{current_user.name}"
        }
        User.update_settings(current_user.id, default_settings)
        user_settings = default_settings

    # Make sure agent is enabled in settings
    if not user_settings.get('agent_enabled', False):
        # Enable the agent in settings
        User.update_settings(current_user.id, {'agent_enabled': True})
        log_action(f"Enabled agent for user {current_user.id}")

    # Create or get an agent for this user
    if current_user.id not in agents:
        log_action(f"Creating new agent for user {current_user.id}")
        agents[current_user.id] = EmailAgent(current_user.id, app=app)
    else:
        log_action(f"Using existing agent for user {current_user.id}")
        # Make sure the agent has an app instance
        if not agents[current_user.id].app:
            agents[current_user.id].app = app

    # Make sure the agent is not already running
    if agents[current_user.id].running:
        log_action(f"Agent for user {current_user.id} is already running")
        return jsonify({'status': 'Agent already running'})

    # Start the agent
    try:
        # Check if the user exists in the database
        user = User.query.get(current_user.id)
        if not user:
            log_action(f"User {current_user.id} not found in database")
            return jsonify({'status': 'Error: User not found in database. Please sign in again.'})

        # Check if the user has an OAuth token
        oauth_token = OAuthToken.query.filter_by(user_id=current_user.id).first()
        if not oauth_token:
            log_action(f"No OAuth token found for user {current_user.id}")
            return jsonify({'status': 'Error: No OAuth token found. Please re-authenticate.'})

        # Check if we can get a Gmail service
        log_action(f"Checking Gmail service for user {current_user.id}")
        service = get_gmail_service(user_id=current_user.id)
        if not service:
            log_action(f"Failed to get Gmail service for user {current_user.id}")
            return jsonify({'status': 'Error: Could not connect to Gmail. Please re-authenticate.'})
        log_action(f"Gmail service available for user {current_user.id}")

        # Try to start the agent
        log_action(f"Attempting to start agent for user {current_user.id}")
        success = agents[current_user.id].start()

        if success:
            # Set the global agent_running flag for backward compatibility
            agent_running = True
            log_action(f"Started email agent for user {current_user.id}")
            return jsonify({'status': 'Agent started'})
        else:
            log_action(f"Failed to start email agent for user {current_user.id}")
            # Check if the agent is actually running despite reporting failure
            if agents[current_user.id].running:
                agent_running = True
                log_action(f"Agent appears to be running despite reporting failure")
                return jsonify({'status': 'Agent started (with warnings)'})
            return jsonify({'status': 'Failed to start agent. Check logs for details.'})
    except Exception as e:
        log_action(f"Error starting agent: {str(e)}")
        return jsonify({'status': f'Error: {str(e)}'})

@app.route('/stop', methods=['POST'])
def stop_agent():
    """Stop the agent"""
    global agent_running, agent_thread

    # Check if user is authenticated
    if not current_user.is_authenticated:
        return jsonify({'status': 'Error: Not authenticated. Please sign in with Google.'})

    # Reset the status tracking in the get_status function
    if hasattr(get_status, 'last_stopped_time'):
        get_status.last_stopped_time = time.time()

    # Reset the last status to ensure proper logging of status changes
    if hasattr(get_status, 'last_status'):
        get_status.last_status = False

    # Stop the agent for this user if it exists
    user_agent_stopped = False
    if current_user.id in agents:
        if agents[current_user.id].running:
            log_action(f"Stopping email agent for user {current_user.id}")
            success = agents[current_user.id].stop()
            if success:
                # Set the global agent_running flag to False for backward compatibility
                agent_running = False
                user_agent_stopped = True
                log_action(f"Stopped email agent for user {current_user.id}")
            else:
                log_action(f"Failed to stop email agent for user {current_user.id}")
                return jsonify({'status': 'Failed to stop agent'})
        else:
            log_action(f"Agent for user {current_user.id} is already stopped")
            user_agent_stopped = True

    # For backward compatibility, also handle the global agent thread
    if agent_running:
        agent_running = False
        log_action("Global agent stop requested")

        # Wait for the thread to finish (with timeout)
        if agent_thread and agent_thread.is_alive():
            log_action("Waiting for global agent thread to terminate...")
            try:
                # Use a longer timeout in production mode
                timeout = 3.0 if is_production() else 2.0
                agent_thread.join(timeout=timeout)

                # Check if thread is still alive after timeout
                if agent_thread.is_alive():
                    log_action("Warning: Global agent thread did not terminate within timeout")
                else:
                    log_action("Global agent thread terminated successfully")
            except Exception as e:
                log_action(f"Error waiting for global thread to terminate: {str(e)}")

        # Reset the thread variable
        agent_thread = None

        # Return success if we stopped either the user agent or the global agent
        if user_agent_stopped:
            return jsonify({'status': 'Agent stopped'})
        else:
            return jsonify({'status': 'Global agent stopped'})

    # If we didn't stop anything but the user agent was already stopped
    if user_agent_stopped:
        return jsonify({'status': 'Agent stopped'})

    # If nothing was running
    return jsonify({'status': 'Agent already stopped'})


@app.route('/reset_database', methods=['GET', 'POST'])
def reset_database():
    """Delete all data from the database"""
    try:
        # Stop the agent if it's running
        global agent_running
        if agent_running:
            agent_running = False
            log_action("Agent stopped for database reset")

        # Delete all data from tables
        UserLog.query.delete()
        OAuthToken.query.delete()
        UserSettings.query.delete()
        User.query.delete()

        # Commit the changes
        db.session.commit()

        # Clear session
        session.clear()

        flash('Database has been reset successfully', 'success')
        return redirect(url_for('login'))
    except Exception as e:
        flash(f'Error resetting database: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# Serve favicon
@app.route('/favicon.ico')
def favicon():
    return app.send_static_file('favicon.ico')

# SEO Routes
@app.route('/sitemap.xml')
def sitemap():
    """Generate dynamic sitemap.xml for search engines"""
    from datetime import datetime
    
    base_url = get_base_url()
    
    # Define pages with their priorities and change frequencies
    pages = [
        {
            'url': base_url,
            'lastmod': datetime.now().strftime('%Y-%m-%d'),
            'changefreq': 'weekly',
            'priority': '1.0'
        },
        {
            'url': f"{base_url}/login",
            'lastmod': datetime.now().strftime('%Y-%m-%d'),
            'changefreq': 'monthly',
            'priority': '0.8'
        },
        {
            'url': f"{base_url}/signup",
            'lastmod': datetime.now().strftime('%Y-%m-%d'),
            'changefreq': 'monthly',
            'priority': '0.9'
        },
        {
            'url': f"{base_url}/privacy-policy",
            'lastmod': datetime.now().strftime('%Y-%m-%d'),
            'changefreq': 'yearly',
            'priority': '0.5'
        },
        {
            'url': f"{base_url}/terms-of-service",
            'lastmod': datetime.now().strftime('%Y-%m-%d'),
            'changefreq': 'yearly',
            'priority': '0.5'
        }
    ]
    
    # Generate XML sitemap
    sitemap_xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    sitemap_xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'
    
    for page in pages:
        sitemap_xml += '  <url>\n'
        sitemap_xml += f'    <loc>{page["url"]}</loc>\n'
        sitemap_xml += f'    <lastmod>{page["lastmod"]}</lastmod>\n'
        sitemap_xml += f'    <changefreq>{page["changefreq"]}</changefreq>\n'
        sitemap_xml += f'    <priority>{page["priority"]}</priority>\n'
        sitemap_xml += '  </url>\n'
    
    sitemap_xml += '</urlset>'
    
    response = make_response(sitemap_xml)
    response.headers['Content-Type'] = 'application/xml'
    response.headers['Cache-Control'] = 'public, max-age=86400'  # Cache for 24 hours
    return response

@app.route('/robots.txt')
def robots():
    """Serve robots.txt for search engines"""
    response = make_response(app.send_static_file('robots.txt'))
    response.headers['Content-Type'] = 'text/plain'
    response.headers['Cache-Control'] = 'public, max-age=86400'  # Cache for 24 hours
    return response

# Additional SEO pages
@app.route('/privacy-policy')
def privacy_policy():
    """Privacy policy page"""
    seo_data = get_seo_data('privacy')
    return render_template('privacy_policy.html', seo=seo_data)

@app.route('/terms-of-service')
def terms_of_service():
    """Terms of service page"""
    seo_data = get_seo_data('terms')
    return render_template('terms_of_service.html', seo=seo_data)

# Google Search Console verification (add your verification file when needed)
@app.route('/google<verification_code>.html')
def google_verification(verification_code):
    """Handle Google Search Console verification files"""
    # Return a simple verification response
    return f"google-site-verification: google{verification_code}.html"

# Error handlers
@app.errorhandler(404)
def not_found_error(_):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(_):
    return render_template('errors/500.html'), 500

# Admin routes for database viewing
@app.route('/admin')
@admin_required
def admin_dashboard():
    """Admin dashboard"""
    return render_template('admin/dashboard.html')

@app.route('/admin/database')
@admin_required
def admin_database():
    """Database viewer dashboard"""
    return render_template('admin/database.html')

@app.route('/admin/api/tables')
@admin_required
def admin_list_tables():
    """List all tables in the database"""
    try:
        # Get database URL from environment
        database_url = os.environ.get('DATABASE_URL')
        if not database_url:
            return jsonify({'error': 'DATABASE_URL environment variable not set'}), 500

        # Enhanced connection handling for PostgreSQL
        conn_params = {}

        # Parse the connection string
        if '?' in database_url:
            # Extract the base connection string
            conn_string = database_url.split('?')[0]

            # Parse additional parameters
            params_str = database_url.split('?')[1]
            for param in params_str.split('&'):
                if '=' in param:
                    key, value = param.split('=')
                    conn_params[key] = value
        else:
            conn_string = database_url

        # Ensure SSL is enabled for PostgreSQL
        if conn_string.startswith('postgresql://'):
            conn_params['sslmode'] = 'require'
            conn_params['application_name'] = 'email_agent_admin'
            conn_params['connect_timeout'] = '10'

        # Connect to the database with enhanced parameters
        conn = psycopg2.connect(conn_string, **conn_params)
        cur = conn.cursor()

        # Get all tables
        cur.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            ORDER BY table_name
        """)
        tables = [table[0] for table in cur.fetchall()]

        # Close connection
        cur.close()
        conn.close()

        return jsonify({'tables': tables})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/admin/api/table/<table_name>')
@admin_required
def admin_view_table(table_name):
    """View data in a specific table with horizontal scrolling"""
    try:
        # Get limit parameter, default to 100
        limit = request.args.get('limit', 100, type=int)

        # Get database URL from environment
        database_url = os.environ.get('DATABASE_URL')
        if not database_url:
            return jsonify({'error': 'DATABASE_URL environment variable not set'}), 500

        # Enhanced connection handling for PostgreSQL
        conn_params = {}

        # Parse the connection string
        if '?' in database_url:
            # Extract the base connection string
            conn_string = database_url.split('?')[0]

            # Parse additional parameters
            params_str = database_url.split('?')[1]
            for param in params_str.split('&'):
                if '=' in param:
                    key, value = param.split('=')
                    conn_params[key] = value
        else:
            conn_string = database_url

        # Ensure SSL is enabled for PostgreSQL
        if conn_string.startswith('postgresql://'):
            conn_params['sslmode'] = 'require'
            conn_params['application_name'] = 'email_agent_admin'
            conn_params['connect_timeout'] = '10'

        # Connect to the database with enhanced parameters
        conn = psycopg2.connect(conn_string, **conn_params)

        # Validate table name to prevent SQL injection
        cur = conn.cursor()
        cur.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name = %s
            )
        """, (table_name,))

        if not cur.fetchone()[0]:
            cur.close()
            conn.close()
            return jsonify({'error': f"Table '{table_name}' does not exist"}), 404

        # Get column names
        cur.execute(f"""
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_schema = 'public'
            AND table_name = %s
            ORDER BY ordinal_position
        """, (table_name,))

        columns_info = cur.fetchall()
        columns = [{'name': col[0], 'type': col[1]} for col in columns_info]

        # Get data with column names
        cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        cur.execute(f'SELECT * FROM "{table_name}" LIMIT %s', (limit,))
        records = cur.fetchall()

        # Count total records
        cur.execute(f'SELECT COUNT(*) FROM "{table_name}"')
        total_count = cur.fetchone()[0]

        # Convert records to list of dicts
        data = []
        for record in records:
            row = {}
            for col in columns:
                value = record[col['name']]
                # Convert non-serializable types to strings
                if isinstance(value, datetime):
                    value = value.isoformat()
                row[col['name']] = value
            data.append(row)

        # Close connection
        cur.close()
        conn.close()

        # Create response with headers for better table display
        response_data = {
            'table': table_name,
            'columns': columns,
            'data': data,
            'count': len(data),
            'total': total_count,
            'limit': limit
        }

        response = make_response(jsonify(response_data))
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['Cache-Control'] = 'no-cache'
        return response
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/admin/api/query', methods=['POST'])
@admin_required
def admin_execute_query():
    """Execute a custom SQL query"""
    try:
        # Get query from request
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({'error': 'No query provided'}), 400

        query = data['query']

        # Prevent destructive queries in production
        if is_production():
            # Check for potentially destructive operations
            if re.search(r'\b(DROP|DELETE|TRUNCATE|ALTER|UPDATE|INSERT)\b', query, re.IGNORECASE):
                return jsonify({
                    'error': 'Destructive operations (DROP, DELETE, TRUNCATE, ALTER, UPDATE, INSERT) are not allowed in production'
                }), 403

        # Get database URL from environment
        database_url = os.environ.get('DATABASE_URL')
        if not database_url:
            return jsonify({'error': 'DATABASE_URL environment variable not set'}), 500

        # Enhanced connection handling for PostgreSQL
        conn_params = {}

        # Parse the connection string
        if '?' in database_url:
            # Extract the base connection string
            conn_string = database_url.split('?')[0]

            # Parse additional parameters
            params_str = database_url.split('?')[1]
            for param in params_str.split('&'):
                if '=' in param:
                    key, value = param.split('=')
                    conn_params[key] = value
        else:
            conn_string = database_url

        # Ensure SSL is enabled for PostgreSQL
        if conn_string.startswith('postgresql://'):
            conn_params['sslmode'] = 'require'
            conn_params['application_name'] = 'email_agent_admin_query'
            conn_params['connect_timeout'] = '10'

        # Connect to the database with enhanced parameters
        conn = psycopg2.connect(conn_string, **conn_params)
        cur = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Execute the query
        cur.execute(query)

        # Check if the query returns data
        if cur.description:
            # Get column names
            columns = [{'name': desc[0], 'type': 'unknown'} for desc in cur.description]

            # Fetch results
            records = cur.fetchall()

            # Convert records to list of dicts
            data = []
            for record in records:
                row = {}
                for i, col in enumerate(columns):
                    value = record[i]
                    # Convert non-serializable types to strings
                    if isinstance(value, datetime):
                        value = value.isoformat()
                    row[col['name']] = value
                data.append(row)

            result = {
                'columns': columns,
                'data': data,
                'count': len(data),
                'message': f'Query returned {len(data)} rows'
            }
        else:
            # For non-SELECT queries
            conn.commit()
            result = {
                'message': f'Query executed successfully. Rows affected: {cur.rowcount}'
            }

        # Close connection
        cur.close()
        conn.close()

        # Create response with headers for better table display
        response = make_response(jsonify(result))
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['Cache-Control'] = 'no-cache'
        return response
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/automated_emails', methods=['GET'])
@login_required
def get_automated_emails():
    """Get all automated email addresses for the current user"""
    from models import AutomatedEmail
    emails = AutomatedEmail.query.filter_by(user_id=current_user.id).all()
    return jsonify({
        'emails': [{
            'id': email.id,
            'email_address': email.email_address,
            'created_at': email.created_at.isoformat()
        } for email in emails]
    })

@app.route('/automated_emails', methods=['POST'])
@login_required
def add_automated_email():
    """Add a new automated email address"""
    from models import AutomatedEmail
    data = request.get_json()
    if not data or 'email_address' not in data:
        return jsonify({'error': 'Email address is required'}), 400

    email_address = data['email_address'].strip()
    if not email_address:
        return jsonify({'error': 'Email address cannot be empty'}), 400

    # Basic email validation
    if '@' not in email_address or '.' not in email_address:
        return jsonify({'error': 'Invalid email address format'}), 400

    success = AutomatedEmail.add_automated_email(current_user.id, email_address)
    if success:
        log_action(f"Added automated email: {email_address}")
        return jsonify({'success': True, 'message': f'Added {email_address} to automated emails'})
    else:
        return jsonify({'error': 'Email address already exists'}), 400

@app.route('/automated_emails/<int:email_id>', methods=['DELETE'])
@login_required
def remove_automated_email(email_id):
    """Remove an automated email address"""
    from models import AutomatedEmail, db
    email = AutomatedEmail.query.filter_by(id=email_id, user_id=current_user.id).first()
    if not email:
        return jsonify({'error': 'Email not found'}), 404

    email_address = email.email_address
    db.session.delete(email)
    db.session.commit()
    log_action(f"Removed automated email: {email_address}")
    return jsonify({'success': True, 'message': f'Removed {email_address} from automated emails'})

@app.route('/api/email_replies', methods=['GET'])
@login_required
@limiter.exempt  # Exempt this route from rate limiting
def get_email_replies():
    """Get all pending email replies for the current user"""
    try:
        from models import EmailReply
        replies = EmailReply.get_pending_replies(current_user.id)
        return jsonify({
            'replies': [{
                'id': reply.id,
                'email_id': reply.email_id,
                'sender': reply.sender,
                'recipient': reply.recipient,
                'subject': reply.subject,
                'original_body': reply.original_body,
                'reply_body': reply.reply_body,
                'modified_reply': reply.modified_reply,
                'status': reply.status,
                'tone': reply.tone,
                'length': reply.length,
                'custom_instructions': reply.custom_instructions,
                'created_at': reply.created_at.isoformat()
            } for reply in replies]
        })
    except Exception as e:
        print(f"Error getting email replies: {e}")
        return jsonify({
            'error': 'An error occurred while loading email replies',
            'details': str(e)
        }), 500

@app.route('/api/email_replies/<int:reply_id>', methods=['GET'])
@login_required
@limiter.exempt  # Exempt this route from rate limiting
def get_email_reply(reply_id):
    """Get a specific email reply"""
    try:
        from models import EmailReply
        reply = EmailReply.get_reply(reply_id, current_user.id)
        if not reply:
            return jsonify({'error': 'Reply not found'}), 404

        return jsonify({
            'id': reply.id,
            'email_id': reply.email_id,
            'sender': reply.sender,
            'recipient': reply.recipient,
            'subject': reply.subject,
            'original_body': reply.original_body,
            'reply_body': reply.reply_body,
            'modified_reply': reply.modified_reply,
            'status': reply.status,
            'tone': reply.tone,
            'length': reply.length,
            'custom_instructions': reply.custom_instructions,
            'created_at': reply.created_at.isoformat()
        })
    except Exception as e:
        print(f"Error getting email reply {reply_id}: {e}")
        return jsonify({
            'error': 'An error occurred while loading the email reply',
            'details': str(e)
        }), 500

@app.route('/api/email_replies/<int:reply_id>/send', methods=['POST'])
@login_required
@limiter.exempt  # Exempt this route from rate limiting
def send_email_reply(reply_id):
    """Send an email reply"""
    from agent import agents

    # Get the agent for this user
    agent = agents.get(current_user.id)
    if not agent:
        # Create a new agent if one doesn't exist
        from agent import EmailAgent
        agent = EmailAgent(current_user.id)
        agents[current_user.id] = agent

    # Send the reply
    success = agent.send_stored_reply(reply_id)
    if success:
        log_action(f"Sent email reply {reply_id}")
        return jsonify({'status': 'success', 'message': 'Email sent successfully'})
    else:
        return jsonify({'status': 'error', 'message': 'Failed to send email'}), 500

@app.route('/api/email_replies/<int:reply_id>/modify', methods=['POST'])
@login_required
@limiter.exempt  # Exempt this route from rate limiting
def modify_email_reply(reply_id):
    """Modify an email reply"""
    from models import EmailReply

    data = request.get_json()
    if not data or 'modified_reply' not in data:
        return jsonify({'error': 'Modified reply is required'}), 400

    modified_reply = data['modified_reply'].strip()
    if not modified_reply:
        return jsonify({'error': 'Modified reply cannot be empty'}), 400

    reply = EmailReply.update_reply(reply_id, current_user.id, modified_reply=modified_reply)
    if reply:
        log_action(f"Modified email reply {reply_id}")
        return jsonify({'status': 'success', 'message': 'Reply updated successfully'})
    else:
        return jsonify({'error': 'Reply not found or could not be updated'}), 404

@app.route('/api/email_replies/<int:reply_id>/regenerate', methods=['POST'])
@login_required
@limiter.exempt  # Exempt this route from rate limiting
def regenerate_email_reply(reply_id):
    """Regenerate an email reply with different parameters"""
    from agent import agents

    data = request.get_json()
    tone = data.get('tone')
    length = data.get('length')
    custom_instructions = data.get('custom_instructions')

    # Get the agent for this user
    agent = agents.get(current_user.id)
    if not agent:
        # Create a new agent if one doesn't exist
        from agent import EmailAgent
        agent = EmailAgent(current_user.id)
        agents[current_user.id] = agent

    # Regenerate the reply
    success = agent.regenerate_reply(reply_id, tone, length, custom_instructions)
    if success:
        log_action(f"Regenerated email reply {reply_id}")

        # Get the updated reply
        from models import EmailReply
        reply = EmailReply.get_reply(reply_id, current_user.id)

        return jsonify({
            'status': 'success',
            'message': 'Reply regenerated successfully',
            'reply': {
                'id': reply.id,
                'reply_body': reply.reply_body,
                'tone': reply.tone,
                'length': reply.length,
                'custom_instructions': reply.custom_instructions
            }
        })
    else:
        return jsonify({'status': 'error', 'message': 'Failed to regenerate reply'}), 500

@app.route('/api/email_replies/<int:reply_id>/discard', methods=['POST'])
@login_required
@limiter.exempt  # Exempt this route from rate limiting
def discard_email_reply(reply_id):
    """Discard an email reply"""
    from models import EmailReply

    success = EmailReply.discard_reply(reply_id, current_user.id)
    if success:
        log_action(f"Discarded email reply {reply_id}")
        return jsonify({'status': 'success', 'message': 'Reply discarded successfully'})
    else:
        return jsonify({'error': 'Reply not found or could not be discarded'}), 404

@app.route('/api/email_replies/discard_all', methods=['POST'])
@login_required
@limiter.exempt  # Exempt this route from rate limiting
def discard_all_email_replies():
    """Discard all pending email replies"""
    from models import EmailReply

    count = EmailReply.discard_all_pending_replies(current_user.id)
    log_action(f"Discarded all pending email replies ({count} total)")
    return jsonify({
        'status': 'success',
        'message': f'Successfully discarded {count} pending replies',
        'count': count
    })

@app.route('/health')
def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check database connection using SQLAlchemy 2.0 API
        from sqlalchemy import text
        db.session.execute(text('SELECT 1'))
        db.session.commit()

        # Get database info
        database_url = app.config['SQLALCHEMY_DATABASE_URI']
        db_info = {
            'type': 'postgresql' if database_url.startswith('postgresql://') else 'sqlite',
            'connected': True,
            'provider': 'render' if database_url.startswith('postgresql://') else 'local',
        }

        # For PostgreSQL, get more detailed info
        if db_info['type'] == 'postgresql':
            try:
                # Get PostgreSQL version
                version_result = db.session.execute(text('SELECT version()'))
                db_info['version'] = version_result.scalar()

                # Get connection count
                conn_result = db.session.execute(text(
                    "SELECT count(*) FROM pg_stat_activity WHERE datname = current_database()"
                ))
                db_info['active_connections'] = conn_result.scalar()

                # Get database size
                size_result = db.session.execute(text(
                    "SELECT pg_size_pretty(pg_database_size(current_database()))"
                ))
                db_info['size'] = size_result.scalar()

                # Get connection pool info
                pool_info = app.config.get('SQLALCHEMY_ENGINE_OPTIONS', {})
                db_info['pool_size'] = pool_info.get('pool_size', 'not set')
                db_info['max_overflow'] = pool_info.get('max_overflow', 'not set')

            except Exception as db_err:
                db_info['detail_error'] = str(db_err)

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'database': db_info,
            'agent_status': 'running' if agent_running else 'stopped',
            'environment': 'production' if is_production() else 'development',
            'version': '1.0.0',
            'render': os.environ.get('RENDER') is not None
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'database': {
                'connected': False,
                'error': str(e)
            },
            'environment': 'production' if is_production() else 'development',
            'version': '1.0.0',
            'render': os.environ.get('RENDER') is not None
        }), 500

if __name__ == "__main__":
    # Use environment port for deployment, default to 5000 locally
    port = int(os.environ.get("PORT", 5000))
    debug = app.config['DEBUG']

    print(f"Starting server on port {port} with debug={debug}")

    # In development, we can use the Flask development server
    # In production, this should be run with gunicorn or similar
    app.run(host='0.0.0.0', port=port, debug=debug)

