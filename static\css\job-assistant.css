/* Job Assistant Specific Styles */

/* Page Header */
.page-header {
  background: var(--gradient-card);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow-card);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing-md);
}

.page-header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.page-icon {
  width: 60px;
  height: 60px;
  background: var(--gradient-btn-primary);
  border-radius: var(--border-radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.8rem;
  box-shadow: var(--box-shadow-btn);
}

.page-title-section h1 {
  margin: 0;
  font-family: var(--font-primary);
  font-weight: 700;
  color: var(--dark);
  font-size: var(--h1-size);
}

.page-subtitle {
  margin: 0;
  color: var(--gray);
  font-size: var(--body-size);
}

.page-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Processing Overlay */
.processing-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
}

.processing-overlay.show {
  display: flex;
}

.processing-container {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--box-shadow-lg);
  max-width: 400px;
  width: 90%;
}

.processing-spinner {
  margin-bottom: var(--spacing-md);
}

.processing-icon {
  font-size: 3rem;
  color: var(--primary);
  animation: pulse 2s infinite;
}

.processing-text {
  font-size: var(--h3-size);
  font-weight: 600;
  color: var(--dark);
  margin-bottom: var(--spacing-sm);
}

.processing-subtext {
  color: var(--gray);
  font-size: var(--body-size);
}

/* Job Wizard Container */
.job-wizard-container {
  background: var(--white);
  border-radius: var(--border-radius-md);
  box-shadow: var(--box-shadow-card);
  overflow: hidden;
}

/* Wizard Progress */
.wizard-progress {
  background: var(--gradient-bg);
  padding: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow-x: auto;
}

.wizard-progress::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  z-index: 1;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  position: relative;
  z-index: 2;
  min-width: 120px;
  text-align: center;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all var(--transition-fast);
  border: 2px solid transparent;
}

.step-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.step.active .step-number {
  background: var(--white);
  color: var(--primary);
  border-color: var(--white);
  box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.3);
}

.step.active .step-label {
  color: var(--white);
  font-weight: 600;
}

.step.completed .step-number {
  background: var(--success);
  color: var(--white);
  border-color: var(--success);
}

.step.completed .step-label {
  color: var(--white);
}

/* Wizard Content */
.wizard-content {
  padding: var(--spacing-xl);
}

.wizard-step {
  display: none;
}

.wizard-step.active {
  display: block;
}

.step-card {
  background: var(--gradient-card);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-card);
}

.step-header {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.step-header h3 {
  color: var(--dark);
  font-family: var(--font-primary);
  font-weight: 700;
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.step-header h3 i {
  color: var(--primary);
}

.step-header p {
  color: var(--gray);
  margin: 0;
}

/* Upload Area */
.upload-area {
  border: 3px dashed var(--border-color);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-xl);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  background: rgba(255, 255, 255, 0.5);
}

.upload-area:hover {
  border-color: var(--primary);
  background: rgba(7, 122, 125, 0.05);
}

.upload-area.dragover {
  border-color: var(--primary);
  background: rgba(7, 122, 125, 0.1);
  transform: scale(1.02);
}

.upload-icon {
  font-size: 3rem;
  color: var(--primary);
  margin-bottom: var(--spacing-md);
}

.upload-content h4 {
  color: var(--dark);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.upload-content p {
  color: var(--gray);
  margin-bottom: var(--spacing-sm);
}

.upload-content small {
  color: var(--gray);
  font-size: 0.85rem;
}

/* CV Preview */
.cv-preview {
  background: rgba(45, 170, 158, 0.05);
  border: 1px solid var(--success);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.cv-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.cv-preview-header h5 {
  margin: 0;
  color: var(--success);
  font-weight: 600;
}

.cv-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.cv-info-item {
  background: var(--white);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  border-left: 4px solid var(--primary);
}

.cv-info-item strong {
  color: var(--dark);
  display: block;
  margin-bottom: var(--spacing-xs);
}

/* Extracted Information */
.extracted-info {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.extracted-info h5 {
  color: var(--dark);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.info-section {
  background: var(--white);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.info-section h6 {
  color: var(--primary);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.editable-field {
  width: 100%;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  font-size: 0.9rem;
  transition: all var(--transition-fast);
}

.editable-field:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(7, 122, 125, 0.1);
}

/* Wizard Navigation */
.wizard-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.wizard-navigation .btn {
  min-width: 120px;
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    text-align: center;
  }
  
  .wizard-progress {
    flex-wrap: wrap;
    gap: var(--spacing-md);
  }
  
  .step {
    min-width: 80px;
  }
  
  .step-number {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .step-label {
    font-size: 0.8rem;
  }
  
  .wizard-content {
    padding: var(--spacing-md);
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}

/* Feature Highlight */
.feature-highlight {
  background: rgba(7, 122, 125, 0.05);
  border: 1px solid rgba(7, 122, 125, 0.2);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-md);
}

.feature-icon {
  width: 50px;
  height: 50px;
  background: var(--gradient-btn-primary);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-content h6 {
  color: var(--dark);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
}

.feature-content ul {
  color: var(--gray);
  margin: 0;
  padding-left: var(--spacing-md);
}

.feature-content li {
  margin-bottom: var(--spacing-xs);
}

/* AI Summary */
.ai-summary {
  background: rgba(122, 226, 207, 0.05);
  border: 1px solid var(--secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.ai-summary h6 {
  color: var(--primary);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
}

/* Contact Verification */
.contact-verification {
  margin-top: var(--spacing-md);
}

/* Tone Preview */
.tone-preview {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: rgba(255, 255, 255, 0.8);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--border-color);
}

.tone-preview h6 {
  color: var(--dark);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
}

.preview-card {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-md);
}

.preview-content {
  color: var(--gray);
  font-style: italic;
  line-height: 1.6;
}

/* Application Summary */
.application-summary {
  background: var(--gradient-card);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.summary-section {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.summary-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.summary-section h6 {
  color: var(--primary);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.summary-label {
  font-weight: 500;
  color: var(--dark);
}

.summary-value {
  color: var(--gray);
  text-align: right;
  max-width: 60%;
  word-break: break-word;
}

/* Generated Email */
.generated-email {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
}

.email-preview {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  background: var(--white);
}

.email-header {
  background: var(--light);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.email-field {
  margin-bottom: var(--spacing-xs);
  font-size: 0.9rem;
}

.email-field:last-child {
  margin-bottom: 0;
}

.email-field strong {
  color: var(--dark);
  min-width: 60px;
  display: inline-block;
}

.email-body {
  padding: var(--spacing-lg);
  min-height: 300px;
  border-bottom: 1px solid var(--border-color);
}

.editable-email-body {
  outline: none;
  line-height: 1.6;
  color: var(--dark);
  font-family: var(--font-secondary);
}

.editable-email-body:focus {
  background: rgba(7, 122, 125, 0.02);
  border-radius: var(--border-radius-sm);
}

.email-signature {
  padding: var(--spacing-md);
  background: var(--light);
  font-size: 0.9rem;
  color: var(--gray);
}

.email-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  padding: var(--spacing-lg);
  background: var(--light);
}

/* Form Enhancements */
.form-label {
  font-weight: 600;
  color: var(--dark);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form-label i {
  color: var(--primary);
}

.form-control, .form-select {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--spacing-sm);
  transition: all var(--transition-fast);
  font-size: var(--body-size);
}

.form-control:focus, .form-select:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(7, 122, 125, 0.1);
}

.form-text {
  font-size: 0.85rem;
  color: var(--gray);
  margin-top: var(--spacing-xs);
}

/* Loading States */
.btn.loading {
  position: relative;
  color: transparent !important;
}

.btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.wizard-step.active {
  animation: fadeInUp 0.5s ease-out;
}
