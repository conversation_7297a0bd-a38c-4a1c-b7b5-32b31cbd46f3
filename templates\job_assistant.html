<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    {% set seo_data = seo or get_seo_data('job_assistant') %}
    <title>{{ seo_data.title }}</title>
    <meta name="description" content="{{ seo_data.description }}" />
    <meta name="keywords" content="{{ seo_data.keywords }}" />
    <meta name="author" content="Email Reply Agent" />
    <meta name="robots" content="noindex, nofollow" />
    <link rel="canonical" href="{{ get_canonical_url('job_assistant') }}" />

    <!-- Enhanced Favicon Implementation -->
    <link
      rel="icon"
      type="image/x-icon"
      href="{{ url_for('static', filename='favicon.ico') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="{{ url_for('static', filename='img/favicon-16x16.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="{{ url_for('static', filename='img/favicon-32x32.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="48x48"
      href="{{ url_for('static', filename='img/favicon-48x48.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='img/apple-touch-icon.png') }}"
    />
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />
    <meta name="theme-color" content="#1E90FF" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Montserrat:wght@400;500;600;700;800&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/styles.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard-enhanced.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/job-assistant.css') }}"
    />
  </head>
  <body class="dashboard-body">
    <!-- Toast Container for Notifications -->
    <div class="toast-container" id="toastContainer"></div>

    <!-- Processing Overlay -->
    <div class="processing-overlay" id="processingOverlay">
      <div class="processing-container">
        <div class="processing-spinner">
          <i class="bi bi-briefcase processing-icon"></i>
        </div>
        <div class="processing-text">Processing Your Application</div>
        <div class="processing-subtext">
          Please wait while we generate your personalized email...
        </div>
      </div>
    </div>

    <!-- Topbar -->
    <nav id="topbar">
      <button class="btn" id="sidebarToggle" type="button">
        <i class="bi bi-list"></i>
      </button>
      <a class="navbar-brand" href="{{ url_for('dashboard') }}">
        <i class="bi bi-envelope-paper-heart"></i>
        Email Reply Agent
      </a>
      <div class="topbar-actions">
        <div class="user-info">
          <div class="user-avatar">{{ current_user.name[0] }}</div>
          <div class="user-name">{{ current_user.name }}</div>
        </div>
        <a href="{{ url_for('logout') }}" class="btn-logout">
          <i class="bi bi-box-arrow-right"></i>Logout
        </a>
      </div>
    </nav>

    <!-- Sidebar Overlay (for mobile) -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Sidebar -->
    <div class="sidebar">
      <div class="sidebar-content">
        <ul class="nav flex-column">
          <li class="nav-item">
            <a href="{{ url_for('dashboard') }}" class="nav-link">
              <i class="bi bi-speedometer2"></i>
              Dashboard
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('email_preview') }}" class="nav-link">
              <i class="bi bi-envelope"></i>
              Email Replies
              <span class="badge" id="pending-replies-count">0</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('job_assistant') }}" class="nav-link active">
              <i class="bi bi-briefcase"></i>
              Job Assistant
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('profile') }}" class="nav-link">
              <i class="bi bi-person"></i>
              Profile
            </a>
          </li>
          <li class="nav-item">
            <a href="{{ url_for('settings') }}" class="nav-link">
              <i class="bi bi-gear"></i>
              Settings
            </a>
          </li>
        </ul>
      </div>
    </div>

    <!-- Main Content -->
    <main class="content">
      <div class="container-fluid px-4">
        {% with messages = get_flashed_messages(with_categories=true) %} {% if
        messages %} {% for category, message in messages %}
        <div
          class="alert alert-{{ category }} alert-dismissible fade show"
          role="alert"
        >
          {{ message }}
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="alert"
          ></button>
        </div>
        {% endfor %} {% endif %} {% endwith %}

        <!-- Page Header -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="page-header">
              <div class="page-header-content">
                <div class="page-icon">
                  <i class="bi bi-briefcase"></i>
                </div>
                <div class="page-title-section">
                  <h1 class="page-title">Job Assistant</h1>
                  <p class="page-subtitle">
                    Generate professional job application emails with AI assistance
                  </p>
                </div>
              </div>
              <div class="page-actions">
                <button class="btn btn-outline-primary" id="viewHistoryBtn">
                  <i class="bi bi-clock-history"></i>
                  View History
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Job Assistant Wizard -->
        <div class="row">
          <div class="col-12">
            <div class="job-wizard-container">
              <!-- Progress Steps -->
              <div class="wizard-progress">
                <div class="step active" data-step="1">
                  <div class="step-number">1</div>
                  <div class="step-label">Upload CV</div>
                </div>
                <div class="step" data-step="2">
                  <div class="step-number">2</div>
                  <div class="step-label">Additional Info</div>
                </div>
                <div class="step" data-step="3">
                  <div class="step-number">3</div>
                  <div class="step-label">Job Details</div>
                </div>
                <div class="step" data-step="4">
                  <div class="step-number">4</div>
                  <div class="step-label">HR Contact</div>
                </div>
                <div class="step" data-step="5">
                  <div class="step-number">5</div>
                  <div class="step-label">Preferences</div>
                </div>
                <div class="step" data-step="6">
                  <div class="step-number">6</div>
                  <div class="step-label">Review & Send</div>
                </div>
              </div>

              <!-- Wizard Content -->
              <div class="wizard-content">
                <!-- Step 1: Upload CV -->
                <div class="wizard-step active" id="step-1">
                  <div class="step-card">
                    <div class="step-header">
                      <h3><i class="bi bi-file-earmark-person"></i> Upload Your CV</h3>
                      <p>Upload your CV/Resume to extract your professional information</p>
                    </div>
                    <div class="step-body">
                      <div class="upload-area" id="cvUploadArea">
                        <div class="upload-content">
                          <i class="bi bi-cloud-upload upload-icon"></i>
                          <h4>Drag & Drop your CV here</h4>
                          <p>or click to browse files</p>
                          <small>Supports PDF and DOCX files (max 10MB)</small>
                        </div>
                        <input type="file" id="cvFileInput" accept=".pdf,.docx" hidden>
                      </div>

                      <!-- CV Preview Area -->
                      <div class="cv-preview" id="cvPreview" style="display: none;">
                        <div class="cv-preview-header">
                          <h5><i class="bi bi-check-circle text-success"></i> CV Uploaded Successfully</h5>
                          <button class="btn btn-sm btn-outline-secondary" id="changeCvBtn">
                            <i class="bi bi-arrow-repeat"></i> Change CV
                          </button>
                        </div>
                        <div class="cv-info" id="cvInfo">
                          <!-- CV information will be populated here -->
                        </div>
                      </div>

                      <!-- Extracted Information Preview -->
                      <div class="extracted-info" id="extractedInfo" style="display: none;">
                        <h5><i class="bi bi-person-check"></i> Extracted Information</h5>
                        <p class="text-muted">Review and edit the information we found in your CV:</p>
                        <div class="info-grid" id="infoGrid">
                          <!-- Extracted information will be populated here -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 2: Additional Information -->
                <div class="wizard-step" id="step-2">
                  <div class="step-card">
                    <div class="step-header">
                      <h3><i class="bi bi-chat-text"></i> Additional Information</h3>
                      <p>Tell us more about yourself to personalize your application</p>
                    </div>
                    <div class="step-body">
                      <div class="form-group mb-4">
                        <label for="additionalInfo" class="form-label">
                          <i class="bi bi-pencil-square"></i> Tell us more about yourself
                        </label>
                        <textarea
                          class="form-control"
                          id="additionalInfo"
                          rows="6"
                          placeholder="What motivates you to apply? Any projects or achievements you'd like to highlight? Preferred working environment or values?"
                        ></textarea>
                        <div class="form-text">
                          This information will be used to personalize your application email. You can mention specific achievements, projects, or motivations.
                        </div>
                      </div>

                      <div class="feature-highlight">
                        <div class="feature-icon">
                          <i class="bi bi-lightbulb"></i>
                        </div>
                        <div class="feature-content">
                          <h6>Pro Tips</h6>
                          <ul class="mb-0">
                            <li>Mention specific achievements or projects relevant to the role</li>
                            <li>Explain what motivates you about this opportunity</li>
                            <li>Highlight your unique value proposition</li>
                            <li>Keep it concise but impactful</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 3: Job Details -->
                <div class="wizard-step" id="step-3">
                  <div class="step-card">
                    <div class="step-header">
                      <h3><i class="bi bi-briefcase"></i> Job Details</h3>
                      <p>Provide details about the job you're applying for</p>
                    </div>
                    <div class="step-body">
                      <div class="row">
                        <div class="col-md-6 mb-3">
                          <label for="companyName" class="form-label">
                            <i class="bi bi-building"></i> Company Name *
                          </label>
                          <input type="text" class="form-control" id="companyName" placeholder="Enter company name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                          <label for="positionTitle" class="form-label">
                            <i class="bi bi-person-badge"></i> Position Title *
                          </label>
                          <input type="text" class="form-control" id="positionTitle" placeholder="Enter position title" required>
                        </div>
                      </div>

                      <div class="mb-4">
                        <label for="jobDescription" class="form-label">
                          <i class="bi bi-file-text"></i> Job Description
                        </label>
                        <textarea
                          class="form-control"
                          id="jobDescription"
                          rows="8"
                          placeholder="Paste the job description here. This will help us tailor your application to match the requirements."
                        ></textarea>
                        <div class="form-text">
                          Paste the complete job description to help our AI generate a more targeted application email.
                        </div>
                      </div>

                      <div class="ai-summary" id="jobSummary" style="display: none;">
                        <h6><i class="bi bi-robot"></i> AI Job Analysis</h6>
                        <div id="jobSummaryContent">
                          <!-- AI-generated job summary will appear here -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 4: HR Contact -->
                <div class="wizard-step" id="step-4">
                  <div class="step-card">
                    <div class="step-header">
                      <h3><i class="bi bi-person-lines-fill"></i> HR Contact Information</h3>
                      <p>Provide contact details for sending your application</p>
                    </div>
                    <div class="step-body">
                      <div class="row">
                        <div class="col-md-6 mb-3">
                          <label for="hrEmail" class="form-label">
                            <i class="bi bi-envelope"></i> HR Email Address *
                          </label>
                          <input type="email" class="form-control" id="hrEmail" placeholder="<EMAIL>" required>
                          <div class="form-text">
                            This is where your application email will be sent.
                          </div>
                        </div>
                        <div class="col-md-6 mb-3">
                          <label for="hrName" class="form-label">
                            <i class="bi bi-person"></i> HR Contact Name
                          </label>
                          <input type="text" class="form-control" id="hrName" placeholder="Enter HR contact name (optional)">
                          <div class="form-text">
                            If known, this will personalize the greeting.
                          </div>
                        </div>
                      </div>

                      <div class="contact-verification" id="contactVerification" style="display: none;">
                        <div class="alert alert-info">
                          <i class="bi bi-info-circle"></i>
                          <strong>Email Verification:</strong> We'll verify this email address before sending your application.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 5: Email Preferences -->
                <div class="wizard-step" id="step-5">
                  <div class="step-card">
                    <div class="step-header">
                      <h3><i class="bi bi-sliders"></i> Email Preferences</h3>
                      <p>Customize the tone and style of your application email</p>
                    </div>
                    <div class="step-body">
                      <div class="row">
                        <div class="col-md-4 mb-3">
                          <label for="emailTone" class="form-label">
                            <i class="bi bi-chat-quote"></i> Email Tone
                          </label>
                          <select class="form-select" id="emailTone">
                            <option value="professional" selected>Professional</option>
                            <option value="friendly">Friendly</option>
                            <option value="enthusiastic">Enthusiastic</option>
                            <option value="confident">Confident</option>
                          </select>
                        </div>
                        <div class="col-md-4 mb-3">
                          <label for="emailLanguage" class="form-label">
                            <i class="bi bi-translate"></i> Language
                          </label>
                          <select class="form-select" id="emailLanguage">
                            <option value="english" selected>English</option>
                            <option value="french">French</option>
                            <option value="spanish">Spanish</option>
                            <option value="german">German</option>
                          </select>
                        </div>
                        <div class="col-md-4 mb-3">
                          <label for="emailType" class="form-label">
                            <i class="bi bi-envelope-paper"></i> Email Type
                          </label>
                          <select class="form-select" id="emailType">
                            <option value="application" selected>Job Application</option>
                            <option value="follow-up">Follow-up</option>
                            <option value="interest">General Interest</option>
                          </select>
                        </div>
                      </div>

                      <div class="tone-preview" id="tonePreview">
                        <h6><i class="bi bi-eye"></i> Tone Preview</h6>
                        <div class="preview-card" id="previewCard">
                          <div class="preview-content">
                            <strong>Professional:</strong> "I am writing to express my interest in the [Position] role at [Company]. With my background in [Field], I believe I would be a valuable addition to your team."
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Step 6: Review & Send -->
                <div class="wizard-step" id="step-6">
                  <div class="step-card">
                    <div class="step-header">
                      <h3><i class="bi bi-check-circle"></i> Review & Send</h3>
                      <p>Review your application details and generate your email</p>
                    </div>
                    <div class="step-body">
                      <div class="application-summary" id="applicationSummary">
                        <!-- Application summary will be populated here -->
                      </div>

                      <div class="generated-email" id="generatedEmail" style="display: none;">
                        <h6><i class="bi bi-envelope-paper"></i> Generated Email</h6>
                        <div class="email-preview">
                          <div class="email-header">
                            <div class="email-field">
                              <strong>To:</strong> <span id="emailTo"></span>
                            </div>
                            <div class="email-field">
                              <strong>Subject:</strong> <span id="emailSubject"></span>
                            </div>
                          </div>
                          <div class="email-body">
                            <div id="emailBody" contenteditable="true" class="editable-email-body">
                              <!-- Generated email body will appear here -->
                            </div>
                          </div>
                          <div class="email-signature">
                            <div id="emailSignature">
                              <!-- Email signature will appear here -->
                            </div>
                          </div>
                        </div>

                        <div class="email-actions mt-3">
                          <button class="btn btn-outline-primary" id="regenerateBtn">
                            <i class="bi bi-arrow-repeat"></i> Regenerate Email
                          </button>
                          <button class="btn btn-success" id="sendEmailBtn">
                            <i class="bi bi-send"></i> Send Application
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="wizard-navigation">
                  <button class="btn btn-outline-secondary" id="prevBtn" style="display: none;">
                    <i class="bi bi-arrow-left"></i> Previous
                  </button>
                  <button class="btn btn-primary" id="nextBtn" disabled>
                    Next <i class="bi bi-arrow-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/unified-dashboard.js') }}"></script>
    <script src="{{ url_for('static', filename='js/job-assistant.js') }}"></script>
  </body>
</html>
