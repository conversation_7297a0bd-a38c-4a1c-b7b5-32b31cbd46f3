/* Job Application Assistant Styles */

.job-app-assistant {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 2rem;
}

.job-app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.job-app-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.job-app-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 1rem;
}

/* Progress Bar */
.progress-container {
  background: #f8f9fa;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
}

.progress-bar-custom {
  background: #e9ecef;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 14.28%; /* Default for step 1 of 7 */
}

.step-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
  color: #6c757d;
}

.step-indicators {
  display: flex;
  gap: 0.5rem;
}

.step-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #e9ecef;
  transition: background-color 0.3s ease;
}

.step-dot.active {
  background: #667eea;
}

.step-dot.completed {
  background: #28a745;
}

/* Step Content */
.step-content {
  padding: 2rem;
}

.step {
  display: none;
}

.step.active {
  display: block;
}

.step h3 {
  color: #2c3e50;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  font-weight: 600;
}

.step p {
  color: #6c757d;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Form Controls */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #2c3e50;
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-control.error {
  border-color: #dc3545;
}

.form-control.error:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* File Upload */
.file-upload {
  position: relative;
  display: inline-block;
  width: 100%;
}

.file-upload-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-label {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  border: 2px dashed #e9ecef;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.file-upload-label:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.file-upload-label i {
  font-size: 2rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.file-upload-text {
  color: #6c757d;
}

.file-upload-text strong {
  color: #2c3e50;
}

.file-selected {
  background: #f0f4ff;
  border-color: #667eea;
}

.file-selected .file-upload-text {
  color: #667eea;
}

/* CV Data Fields */
.cv-data-fields {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
}

.row {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.col {
  flex: 1;
}

.col-2 {
  flex: 0 0 calc(50% - 0.75rem);
}

/* Skills Tags */
.skills-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.skill-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-outline:hover {
  background: #667eea;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Button Groups */
.button-group {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
}

/* AI Features */
.ai-feature {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  text-align: center;
}

.ai-feature h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.ai-feature p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

/* Loading States */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Email Preview */
.email-preview {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1rem 0;
}

.email-preview h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.email-subject {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.email-body {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  white-space: pre-wrap;
  line-height: 1.6;
  max-height: 400px;
  overflow-y: auto;
}

/* Rephrase Feature */
.rephrase-container {
  margin-top: 1rem;
  padding: 1rem;
  background: #f0f4ff;
  border-radius: 8px;
  border: 1px solid #667eea;
}

.rephrase-input {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

/* Results Display */
.results-container {
  text-align: center;
  padding: 2rem;
}

.success-icon {
  font-size: 4rem;
  color: #28a745;
  margin-bottom: 1rem;
}

.results-container h3 {
  color: #28a745;
  margin-bottom: 1rem;
}

.application-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  text-align: left;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
}

.summary-row:last-child {
  border-bottom: none;
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transform: translateX(400px);
  transition: transform 0.3s ease;
}

.toast.show {
  transform: translateX(0);
}

.toast.success {
  border-left: 4px solid #28a745;
}

.toast.error {
  border-left: 4px solid #dc3545;
}

.toast.info {
  border-left: 4px solid #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .job-app-assistant {
    margin: 0 -1rem;
    border-radius: 0;
  }

  .job-app-header {
    padding: 1.5rem;
  }

  .job-app-header h2 {
    font-size: 1.5rem;
  }

  .step-content {
    padding: 1.5rem;
  }

  .row {
    flex-direction: column;
    gap: 1rem;
  }

  .col-2 {
    flex: 1;
  }

  .button-group {
    flex-direction: column;
    gap: 1rem;
  }

  .button-group .btn {
    width: 100%;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step.active {
  animation: fadeIn 0.5s ease;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.ai-feature {
  animation: pulse 2s infinite;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .job-app-assistant {
    background: #2c3e50;
    color: #ecf0f1;
  }

  .step-content {
    background: #2c3e50;
  }

  .form-control {
    background: #34495e;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }

  .form-control:focus {
    border-color: #667eea;
  }

  .email-preview {
    background: #34495e;
    border-color: #4a5f7a;
  }

  .email-subject,
  .email-body {
    background: #2c3e50;
    border-color: #4a5f7a;
    color: #ecf0f1;
  }
}
