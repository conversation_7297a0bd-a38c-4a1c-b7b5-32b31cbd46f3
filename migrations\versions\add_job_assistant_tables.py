"""Add job assistant tables

Revision ID: add_job_assistant_tables
Revises: 728bb708c342
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'add_job_assistant_tables'
down_revision = '728bb708c342'
branch_labels = None
depends_on = None


def upgrade():
    # Add job_applications_sent column to user_stats table
    op.add_column('user_stats', sa.Column('job_applications_sent', sa.Integer(), default=0))
    
    # Create cv_profile table
    op.create_table('cv_profile',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=100), nullable=False),
        sa.Column('full_name', sa.String(length=255), nullable=True),
        sa.Column('email', sa.String(length=255), nullable=True),
        sa.Column('phone', sa.String(length=50), nullable=True),
        sa.Column('address', sa.Text(), nullable=True),
        sa.Column('education', sa.Text(), nullable=True),
        sa.Column('work_experience', sa.Text(), nullable=True),
        sa.Column('skills', sa.Text(), nullable=True),
        sa.Column('languages', sa.Text(), nullable=True),
        sa.Column('additional_info', sa.Text(), nullable=True),
        sa.Column('original_filename', sa.String(length=255), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow, onupdate=datetime.utcnow),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create job_application table
    op.create_table('job_application',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=100), nullable=False),
        sa.Column('cv_profile_id', sa.Integer(), nullable=True),
        sa.Column('company_name', sa.String(length=255), nullable=True),
        sa.Column('position_title', sa.String(length=255), nullable=True),
        sa.Column('job_description', sa.Text(), nullable=True),
        sa.Column('hr_email', sa.String(length=255), nullable=True),
        sa.Column('hr_name', sa.String(length=255), nullable=True),
        sa.Column('email_tone', sa.String(length=50), default='professional'),
        sa.Column('email_language', sa.String(length=50), default='english'),
        sa.Column('email_type', sa.String(length=50), default='application'),
        sa.Column('additional_info', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=50), default='draft'),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow, onupdate=datetime.utcnow),
        sa.ForeignKeyConstraint(['cv_profile_id'], ['cv_profile.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create generated_job_email table
    op.create_table('generated_job_email',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=100), nullable=False),
        sa.Column('job_application_id', sa.Integer(), nullable=False),
        sa.Column('subject', sa.String(length=255), nullable=True),
        sa.Column('body', sa.Text(), nullable=True),
        sa.Column('modified_body', sa.Text(), nullable=True),
        sa.Column('signature', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=50), default='draft'),
        sa.Column('sent_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), default=datetime.utcnow, onupdate=datetime.utcnow),
        sa.ForeignKeyConstraint(['job_application_id'], ['job_application.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    # Drop tables in reverse order
    op.drop_table('generated_job_email')
    op.drop_table('job_application')
    op.drop_table('cv_profile')
    
    # Remove job_applications_sent column from user_stats
    op.drop_column('user_stats', 'job_applications_sent')
