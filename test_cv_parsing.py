"""
Test script for the Job Application Assistant functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import extract_cv_data

def test_cv_parsing():
    """Test CV data extraction"""
    
    # Sample CV text
    sample_cv = """
    John <PERSON>
    Software Engineer
    
    Email: <EMAIL>
    Phone: +****************
    LinkedIn: linkedin.com/in/johndoe
    Location: San Francisco, CA
    
    Summary:
    Experienced software engineer with 5+ years of experience in full-stack development.
    Passionate about creating scalable web applications and working with modern technologies.
    
    Skills:
    Python, JavaScript, React, Node.js, PostgreSQL, Docker, AWS, Git
    
    Experience:
    Senior Software Engineer at Tech Corp (2021-2023)
    - Led development of microservices architecture
    - Improved application performance by 40%
    - Mentored junior developers
    
    Software Developer at StartupCo (2019-2021)
    - Built responsive web applications using React and Node.js
    - Implemented RESTful APIs
    - Collaborated with cross-functional teams
    
    Education:
    Bachelor of Science in Computer Science
    University of Technology (2015-2019)
    """
    
    # Test extraction
    extracted_data = extract_cv_data(sample_cv)
    
    print("Extracted CV Data:")
    print("-" * 50)
    for key, value in extracted_data.items():
        print(f"{key}: {value}")
    
    # Verify extraction
    assert extracted_data['email'] == '<EMAIL>'
    assert extracted_data['phone'] == '+****************'
    assert 'Python' in extracted_data['skills']
    assert 'JavaScript' in extracted_data['skills']
    assert 'linkedin.com/in/johndoe' in extracted_data['linkedin_url']
    
    print("\n✅ CV parsing test passed!")

if __name__ == "__main__":
    test_cv_parsing()
