<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Job Application Assistant - Email Reply Agent</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Custom CSS -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dashboard.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/job-application-assistant.css') }}"
    />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary fixed-top">
      <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('dashboard') }}">
          <i class="fas fa-reply me-2"></i>Email Reply Agent
        </a>

        <div class="navbar-nav ms-auto">
          <div class="nav-item dropdown">
            <a
              class="nav-link dropdown-toggle d-flex align-items-center"
              href="#"
              role="button"
              data-bs-toggle="dropdown"
            >
              {% if current_user.picture %}
              <img
                src="{{ current_user.picture }}"
                alt="Profile"
                class="rounded-circle me-2"
                style="width: 32px; height: 32px"
              />
              {% else %}
              <i class="fas fa-user-circle me-2 fs-4"></i>
              {% endif %} {{ current_user.name }}
            </a>
            <ul class="dropdown-menu dropdown-menu-end">
              <li>
                <a class="dropdown-item" href="{{ url_for('dashboard') }}"
                  ><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a
                >
              </li>
              <li>
                <a class="dropdown-item" href="{{ url_for('profile') }}"
                  ><i class="fas fa-user me-2"></i>Profile</a
                >
              </li>
              <li>
                <a class="dropdown-item" href="{{ url_for('settings') }}"
                  ><i class="fas fa-cog me-2"></i>Settings</a
                >
              </li>
              <li><hr class="dropdown-divider" /></li>
              <li>
                <a class="dropdown-item" href="{{ url_for('logout') }}"
                  ><i class="fas fa-sign-out-alt me-2"></i>Logout</a
                >
              </li>
            </ul>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid" style="margin-top: 80px">
      <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
          <div class="position-sticky pt-3">
            <ul class="nav flex-column">
              <li class="nav-item">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                  <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                </a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link active"
                  href="{{ url_for('job_application_assistant') }}"
                >
                  <i class="fas fa-briefcase me-2"></i>Job Assistant
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ url_for('profile') }}">
                  <i class="fas fa-user me-2"></i>Profile
                </a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="{{ url_for('settings') }}">
                  <i class="fas fa-cog me-2"></i>Settings
                </a>
              </li>
            </ul>
          </div>
        </div>

        <!-- Main Content Area -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
          <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
          >
            <h1 class="h2">
              <i class="fas fa-briefcase me-2"></i>Job Application Assistant
            </h1>
          </div>

          <!-- Job Application Assistant Content -->
          <div class="job-application-assistant">
            <!-- Progress Bar -->
            <div class="progress-container mb-4">
              <div class="progress" style="height: 8px">
                <div
                  class="progress-bar bg-primary"
                  role="progressbar"
                  style="width: 14%"
                  id="jobAppProgress"
                ></div>
              </div>
              <div class="step-indicators mt-2">
                <span class="step-indicator active" data-step="1"
                  >1. CV Upload</span
                >
                <span class="step-indicator" data-step="2"
                  >2. Additional Info</span
                >
                <span class="step-indicator" data-step="3"
                  >3. Job Description</span
                >
                <span class="step-indicator" data-step="4">4. HR Contact</span>
                <span class="step-indicator" data-step="5"
                  >5. Email Preferences</span
                >
                <span class="step-indicator" data-step="6"
                  >6. Review & Send</span
                >
                <span class="step-indicator" data-step="7">7. Results</span>
              </div>
            </div>

            <!-- Step 1: CV Upload and Information Extraction -->
            <div class="step-content" id="jobAppStep1">
              <div class="card">
                <div class="card-header">
                  <h4>
                    <i class="fas fa-file-upload me-2"></i>Step 1: Upload Your
                    CV
                  </h4>
                  <p class="mb-0 text-muted">
                    Upload your CV and we'll automatically extract your
                    information
                  </p>
                </div>
                <div class="card-body">
                  <div class="mb-4">
                    <label for="cvFile" class="form-label"
                      >Choose CV File</label
                    >
                    <input
                      type="file"
                      class="form-control"
                      id="cvFile"
                      accept=".pdf,.doc,.docx,.txt"
                    />
                    <div class="form-text">
                      Supported formats: PDF, DOC, DOCX, TXT (Max size: 10MB)
                    </div>
                  </div>

                  <div
                    id="cvParseStatus"
                    class="mb-3"
                    style="display: none"
                  ></div>

                  <div id="extractedFields" style="display: none">
                    <h5 class="mb-3">
                      Extracted Information (Please review and edit)
                    </h5>
                    <div class="row">
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="cvFullName" class="form-label"
                            >Full Name</label
                          >
                          <input
                            type="text"
                            class="form-control"
                            id="cvFullName"
                          />
                        </div>
                        <div class="mb-3">
                          <label for="cvEmail" class="form-label">Email</label>
                          <input
                            type="email"
                            class="form-control"
                            id="cvEmail"
                          />
                        </div>
                        <div class="mb-3">
                          <label for="cvPhone" class="form-label">Phone</label>
                          <input type="tel" class="form-control" id="cvPhone" />
                        </div>
                        <div class="mb-3">
                          <label for="cvLocation" class="form-label"
                            >Location</label
                          >
                          <input
                            type="text"
                            class="form-control"
                            id="cvLocation"
                          />
                        </div>
                      </div>
                      <div class="col-md-6">
                        <div class="mb-3">
                          <label for="cvLinkedIn" class="form-label"
                            >LinkedIn URL</label
                          >
                          <input
                            type="url"
                            class="form-control"
                            id="cvLinkedIn"
                          />
                        </div>
                        <div class="mb-3">
                          <label for="cvSkills" class="form-label"
                            >Skills</label
                          >
                          <textarea
                            class="form-control"
                            id="cvSkills"
                            rows="3"
                            placeholder="e.g., Python, JavaScript, React, SQL"
                          ></textarea>
                        </div>
                        <div class="mb-3">
                          <label for="cvEducation" class="form-label"
                            >Education</label
                          >
                          <textarea
                            class="form-control"
                            id="cvEducation"
                            rows="2"
                          ></textarea>
                        </div>
                      </div>
                    </div>
                    <div class="mb-3">
                      <label for="cvExperience" class="form-label"
                        >Experience Summary</label
                      >
                      <textarea
                        class="form-control"
                        id="cvExperience"
                        rows="4"
                      ></textarea>
                    </div>
                    <div class="mb-3">
                      <label for="cvSummary" class="form-label"
                        >Professional Summary</label
                      >
                      <textarea
                        class="form-control"
                        id="cvSummary"
                        rows="3"
                      ></textarea>
                    </div>
                    <div class="form-check mb-3">
                      <input
                        type="checkbox"
                        class="form-check-input"
                        id="saveProfileCheckbox"
                        checked
                      />
                      <label class="form-check-label" for="saveProfileCheckbox">
                        Save this information to my profile for future
                        applications
                      </label>
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-primary"
                    id="jobAppNext1"
                    disabled
                  >
                    Next: Additional Information
                    <i class="fas fa-arrow-right ms-1"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 2: Additional Information -->
            <div class="step-content" id="jobAppStep2" style="display: none">
              <div class="card">
                <div class="card-header">
                  <h4>
                    <i class="fas fa-user-plus me-2"></i>Step 2: Additional
                    Information
                  </h4>
                  <p class="mb-0 text-muted">
                    Provide additional details to personalize your application
                  </p>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="yearsExperience" class="form-label"
                          >Years of Experience</label
                        >
                        <select class="form-control" id="yearsExperience">
                          <option value="">Select experience level</option>
                          <option value="0-1">0-1 years</option>
                          <option value="2-3">2-3 years</option>
                          <option value="4-5">4-5 years</option>
                          <option value="6-8">6-8 years</option>
                          <option value="9-12">9-12 years</option>
                          <option value="13+">13+ years</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="currentPosition" class="form-label"
                          >Current Position</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="currentPosition"
                          placeholder="e.g., Senior Software Engineer"
                        />
                      </div>
                      <div class="mb-3">
                        <label for="portfolioUrl" class="form-label"
                          >Portfolio/Website URL</label
                        >
                        <input
                          type="url"
                          class="form-control"
                          id="portfolioUrl"
                          placeholder="https://yourportfolio.com"
                        />
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="availability" class="form-label"
                          >Availability</label
                        >
                        <select class="form-control" id="availability">
                          <option value="">Select availability</option>
                          <option value="immediate">Immediate</option>
                          <option value="2weeks">2 weeks notice</option>
                          <option value="1month">1 month notice</option>
                          <option value="negotiable">Negotiable</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label for="salaryExpectation" class="form-label"
                          >Salary Expectation (Optional)</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="salaryExpectation"
                          placeholder="e.g., $80,000 - $100,000"
                        />
                      </div>
                      <div class="mb-3">
                        <label for="githubUrl" class="form-label"
                          >GitHub URL</label
                        >
                        <input
                          type="url"
                          class="form-control"
                          id="githubUrl"
                          placeholder="https://github.com/yourusername"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label for="whyInterested" class="form-label"
                      >Why are you interested in this position?</label
                    >
                    <textarea
                      class="form-control"
                      id="whyInterested"
                      rows="3"
                      placeholder="Explain your motivation and interest in the role..."
                    ></textarea>
                  </div>
                  <div class="mb-3">
                    <label for="coverLetterPoints" class="form-label"
                      >Key Points to Highlight</label
                    >
                    <textarea
                      class="form-control"
                      id="coverLetterPoints"
                      rows="3"
                      placeholder="Any specific achievements, projects, or skills you want to emphasize..."
                    ></textarea>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-secondary me-2"
                    id="jobAppPrev2"
                  >
                    <i class="fas fa-arrow-left me-1"></i> Previous
                  </button>
                  <button
                    type="button"
                    class="btn btn-primary"
                    id="jobAppNext2"
                  >
                    Next: Job Description
                    <i class="fas fa-arrow-right ms-1"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 3: Job Description -->
            <div class="step-content" id="jobAppStep3" style="display: none">
              <div class="card">
                <div class="card-header">
                  <h4>
                    <i class="fas fa-briefcase me-2"></i>Step 3: Job Description
                  </h4>
                  <p class="mb-0 text-muted">
                    Paste the job description to help us customize your
                    application
                  </p>
                </div>
                <div class="card-body">
                  <div class="mb-3">
                    <label for="jobTitle" class="form-label">Job Title</label>
                    <input
                      type="text"
                      class="form-control"
                      id="jobTitle"
                      placeholder="e.g., Senior Frontend Developer"
                      required
                    />
                  </div>
                  <div class="mb-3">
                    <label for="companyName" class="form-label"
                      >Company Name</label
                    >
                    <input
                      type="text"
                      class="form-control"
                      id="companyName"
                      placeholder="e.g., Tech Corp Inc."
                      required
                    />
                  </div>
                  <div class="mb-3">
                    <label for="jobDescription" class="form-label"
                      >Job Description</label
                    >
                    <textarea
                      class="form-control"
                      id="jobDescription"
                      rows="8"
                      placeholder="Paste the complete job description here..."
                      required
                    ></textarea>
                  </div>
                  <div class="mb-3">
                    <button
                      type="button"
                      class="btn btn-outline-primary"
                      id="summarizeJobBtn"
                    >
                      <i class="fas fa-magic me-1"></i> AI Summarize Job
                      Requirements
                    </button>
                  </div>
                  <div id="jobSummary" style="display: none">
                    <h6>AI-Generated Job Summary:</h6>
                    <div class="alert alert-info" id="jobSummaryContent"></div>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-secondary me-2"
                    id="jobAppPrev3"
                  >
                    <i class="fas fa-arrow-left me-1"></i> Previous
                  </button>
                  <button
                    type="button"
                    class="btn btn-primary"
                    id="jobAppNext3"
                  >
                    Next: HR Contact <i class="fas fa-arrow-right ms-1"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 4: HR Contact Information -->
            <div class="step-content" id="jobAppStep4" style="display: none">
              <div class="card">
                <div class="card-header">
                  <h4>
                    <i class="fas fa-address-book me-2"></i>Step 4: HR Contact
                    Information
                  </h4>
                  <p class="mb-0 text-muted">
                    Provide the contact details for your application
                  </p>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="hrName" class="form-label"
                          >HR Manager/Recruiter Name</label
                        >
                        <input
                          type="text"
                          class="form-control"
                          id="hrName"
                          placeholder="e.g., Sarah Johnson"
                        />
                        <div class="form-text">
                          Leave blank if not specified in the job posting
                        </div>
                      </div>
                      <div class="mb-3">
                        <label for="hrEmail" class="form-label"
                          >Email Address *</label
                        >
                        <input
                          type="email"
                          class="form-control"
                          id="hrEmail"
                          placeholder="<EMAIL>"
                          required
                        />
                      </div>
                    </div>
                    <div class="col-md-6">
                      <div class="mb-3">
                        <label for="hrPhone" class="form-label"
                          >Phone Number</label
                        >
                        <input
                          type="tel"
                          class="form-control"
                          id="hrPhone"
                          placeholder="+****************"
                        />
                        <div class="form-text">Optional</div>
                      </div>
                      <div class="mb-3">
                        <label for="applicationDeadline" class="form-label"
                          >Application Deadline</label
                        >
                        <input
                          type="date"
                          class="form-control"
                          id="applicationDeadline"
                        />
                        <div class="form-text">
                          Optional - helps with urgency tone
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-secondary me-2"
                    id="jobAppPrev4"
                  >
                    <i class="fas fa-arrow-left me-1"></i> Previous
                  </button>
                  <button
                    type="button"
                    class="btn btn-primary"
                    id="jobAppNext4"
                  >
                    Next: Email Preferences
                    <i class="fas fa-arrow-right ms-1"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 5: Email Preferences -->
            <div class="step-content" id="jobAppStep5" style="display: none">
              <div class="card">
                <div class="card-header">
                  <h4>
                    <i class="fas fa-envelope-open-text me-2"></i>Step 5: Email
                    Preferences
                  </h4>
                  <p class="mb-0 text-muted">
                    Customize how your application email should be written
                  </p>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="mb-3">
                        <label for="emailTone" class="form-label"
                          >Email Tone</label
                        >
                        <select class="form-control" id="emailTone">
                          <option value="professional">Professional</option>
                          <option value="friendly">
                            Friendly & Professional
                          </option>
                          <option value="formal">Formal</option>
                          <option value="enthusiastic">Enthusiastic</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="mb-3">
                        <label for="emailLanguage" class="form-label"
                          >Language</label
                        >
                        <select class="form-control" id="emailLanguage">
                          <option value="english">English</option>
                          <option value="spanish">Spanish</option>
                          <option value="french">French</option>
                          <option value="german">German</option>
                          <option value="italian">Italian</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="mb-3">
                        <label for="emailType" class="form-label"
                          >Email Type</label
                        >
                        <select class="form-control" id="emailType">
                          <option value="cover_letter">Cover Letter</option>
                          <option value="follow_up">Follow-up</option>
                          <option value="inquiry">Inquiry</option>
                          <option value="thank_you">Thank You</option>
                        </select>
                      </div>
                    </div>
                  </div>
                  <div class="mb-3">
                    <label for="customInstructions" class="form-label"
                      >Custom Instructions (Optional)</label
                    >
                    <textarea
                      class="form-control"
                      id="customInstructions"
                      rows="3"
                      placeholder="Any specific instructions for the AI when generating your email..."
                    ></textarea>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-secondary me-2"
                    id="jobAppPrev5"
                  >
                    <i class="fas fa-arrow-left me-1"></i> Previous
                  </button>
                  <button
                    type="button"
                    class="btn btn-primary"
                    id="jobAppNext5"
                  >
                    Generate Email <i class="fas fa-magic ms-1"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 6: Review and Edit Email -->
            <div class="step-content" id="jobAppStep6" style="display: none">
              <div class="card">
                <div class="card-header">
                  <h4>
                    <i class="fas fa-edit me-2"></i>Step 6: Review & Edit Email
                  </h4>
                  <p class="mb-0 text-muted">
                    Review the generated email and make any necessary edits
                  </p>
                </div>
                <div class="card-body">
                  <div id="emailGenerationStatus" class="mb-3"></div>

                  <div class="mb-3">
                    <label for="emailSubject" class="form-label"
                      >Subject Line</label
                    >
                    <input type="text" class="form-control" id="emailSubject" />
                  </div>

                  <div class="mb-3">
                    <label for="emailBody" class="form-label">Email Body</label>
                    <textarea
                      class="form-control"
                      id="emailBody"
                      rows="15"
                    ></textarea>
                  </div>

                  <div class="mb-3">
                    <div class="row">
                      <div class="col-md-8">
                        <input
                          type="text"
                          class="form-control"
                          id="rephraseRequest"
                          placeholder="e.g., Make it more enthusiastic, Add technical details, Shorten it..."
                        />
                      </div>
                      <div class="col-md-4">
                        <button
                          type="button"
                          class="btn btn-outline-secondary w-100"
                          id="rephraseBtn"
                        >
                          <i class="fas fa-redo me-1"></i> Rephrase
                        </button>
                      </div>
                    </div>
                    <div class="form-text">
                      Ask AI to modify the email based on your instructions
                    </div>
                  </div>

                  <div class="d-flex gap-2 mb-3">
                    <button
                      type="button"
                      class="btn btn-outline-primary"
                      id="regenerateEmailBtn"
                    >
                      <i class="fas fa-sync-alt me-1"></i> Regenerate Email
                    </button>
                    <button
                      type="button"
                      class="btn btn-outline-success"
                      id="previewEmailBtn"
                    >
                      <i class="fas fa-eye me-1"></i> Preview
                    </button>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-secondary me-2"
                    id="jobAppPrev6"
                  >
                    <i class="fas fa-arrow-left me-1"></i> Previous
                  </button>
                  <button
                    type="button"
                    class="btn btn-success"
                    id="sendEmailBtn"
                  >
                    <i class="fas fa-paper-plane me-1"></i> Send Application
                  </button>
                </div>
              </div>
            </div>

            <!-- Step 7: Results -->
            <div class="step-content" id="jobAppStep7" style="display: none">
              <div class="card">
                <div class="card-header">
                  <h4>
                    <i class="fas fa-check-circle me-2"></i>Step 7: Application
                    Sent!
                  </h4>
                </div>
                <div class="card-body">
                  <div id="sendResults"></div>
                  <div class="mt-4">
                    <h6>Application Summary:</h6>
                    <div id="applicationSummary"></div>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-primary me-2"
                    id="newApplicationBtn"
                  >
                    <i class="fas fa-plus me-1"></i> New Application
                  </button>
                  <button
                    type="button"
                    class="btn btn-outline-primary"
                    id="viewHistoryBtn"
                  >
                    <i class="fas fa-history me-1"></i> View History
                  </button>
                </div>
              </div>
            </div>

            <!-- Application History -->
            <div class="mt-5" id="applicationHistory" style="display: none">
              <div class="card">
                <div class="card-header">
                  <h5>
                    <i class="fas fa-history me-2"></i>Application History
                  </h5>
                </div>
                <div class="card-body">
                  <div id="historyContent">
                    <div class="text-center">
                      <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
      <div
        id="toastMessage"
        class="toast"
        role="alert"
        aria-live="assertive"
        aria-atomic="true"
      >
        <div class="toast-header">
          <strong class="me-auto">Job Application Assistant</strong>
          <button
            type="button"
            class="btn-close"
            data-bs-dismiss="toast"
            aria-label="Close"
          ></button>
        </div>
        <div class="toast-body" id="toastBody"></div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/job-application-assistant.js') }}"></script>
    <script>
      // Initialize the Job Application Assistant
      document.addEventListener("DOMContentLoaded", function () {
        window.jobAppAssistant = new JobApplicationAssistant();
      });
    </script>
  </body>
</html>
