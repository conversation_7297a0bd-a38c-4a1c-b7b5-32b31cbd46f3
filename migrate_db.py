#!/usr/bin/env python3
"""
Database migration script for Job Assistant feature
"""

from app import app, db
from sqlalchemy import text
import sys

def migrate_database():
    """Run database migration"""
    with app.app_context():
        try:
            print("Starting database migration...")
            
            # Add the missing column to user_stats table
            try:
                db.session.execute(text('ALTER TABLE user_stats ADD COLUMN job_applications_sent INTEGER DEFAULT 0'))
                db.session.commit()
                print("✅ Added job_applications_sent column to user_stats table")
            except Exception as e:
                print(f"⚠️  Column addition error (might already exist): {e}")
                db.session.rollback()
            
            # Create all new tables
            try:
                db.create_all()
                print("✅ Created/verified all database tables")
            except Exception as e:
                print(f"❌ Error creating tables: {e}")
                return False
            
            print("✅ Database migration completed successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            return False

if __name__ == "__main__":
    success = migrate_database()
    sys.exit(0 if success else 1)
